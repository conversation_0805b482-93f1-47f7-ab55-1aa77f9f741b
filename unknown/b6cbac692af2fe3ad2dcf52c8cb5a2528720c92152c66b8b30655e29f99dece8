/**
 * Developer Log Panel Component
 *
 * A collapsible panel that displays backend logs for the current conversation.
 * Only visible to developers and can be toggled on/off.
 * Styled to match the application's Apple/OpenAI-inspired aesthetic.
 */
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { ClipboardIcon, ChevronDownIcon } from '../../utils/Icons.js';

export default {
    name: 'DevLogPanel',
    props: {
        isVisible: {
            type: Boolean,
            default: false
        },
        logs: {
            // 日志可以是字符串（HTML）或数组
            type: [String, Array],
            default: ''
        },
        activeConversationId: {
            type: String,
            default: null
        }
    },
    setup(props) {
        // 日志面板引用，用于滚动控制
        const logPanelRef = ref(null);
        // 是否自动滚动到底部
        const autoScroll = ref(true);
        // 复制按钮状态
        const copyButtonText = ref('复制日志');
        // 日志内容高度
        const logContentHeight = ref('200px'); // 默认高度

        // 监听日志变化，自动滚动到底部
        watch(() => props.logs, () => {
            if (autoScroll.value && logPanelRef.value) {
                scrollToBottom();
            }
        }, { deep: true });

        // 监听可见性变化，触发内容调整和滚动
        watch(() => props.isVisible, (newValue) => {
            if (newValue) {
                // 显示时，调整内容高度并滚动到底部
                adjustHeight();

                // 确保DOM更新后滚动到底部
                setTimeout(() => {
                    scrollToBottom();
                }, 100);
            }
        });

        // 调整日志面板高度
        const adjustHeight = () => {
            // 根据设备类型设置不同的高度
            // 注意：需要考虑头部高度(~30px)和内边距(~24px)，确保总高度不超过280px
            if (window.innerWidth >= 1024) { // 桌面设备
                logContentHeight.value = '210px'; // 减少高度以适应头部和内边距
            } else if (window.innerWidth >= 768) { // 平板设备
                logContentHeight.value = '180px';
            } else { // 移动设备
                logContentHeight.value = '160px';
            }
        };

        // 滚动到底部
        const scrollToBottom = () => {
            if (logPanelRef.value) {
                logPanelRef.value.scrollTop = logPanelRef.value.scrollHeight;
            }
        };

        // 处理滚动事件
        const handleScroll = () => {
            if (!logPanelRef.value) return;

            // 检查是否在底部附近
            const isAtBottom = logPanelRef.value.scrollHeight - logPanelRef.value.scrollTop - logPanelRef.value.clientHeight < 30;
            autoScroll.value = isAtBottom;
        };

        // 复制日志到剪贴板
        const copyLogs = () => {
            let logText;

            // 根据日志类型处理
            if (typeof props.logs === 'string') {
                // 如果是HTML字符串，提取纯文本
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = props.logs;
                logText = tempDiv.textContent || tempDiv.innerText || props.logs;
            } else if (Array.isArray(props.logs)) {
                // 如果是数组，拼接为字符串
                logText = props.logs.join('\n');
            } else {
                // 其他情况
                logText = String(props.logs || '');
            }

            navigator.clipboard.writeText(logText)
                .then(() => {
                    copyButtonText.value = '已复制';
                    setTimeout(() => {
                        copyButtonText.value = '复制日志';
                    }, 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                    copyButtonText.value = '复制失败';
                    setTimeout(() => {
                        copyButtonText.value = '复制日志';
                    }, 2000);
                });
        };



        // 组件挂载时设置事件监听
        onMounted(() => {
            if (logPanelRef.value) {
                logPanelRef.value.addEventListener('scroll', handleScroll);
            }

            // 监听窗口大小变化
            window.addEventListener('resize', adjustHeight);

            // 初始调整高度
            adjustHeight();
        });

        // 组件卸载时清理事件监听
        onUnmounted(() => {
            if (logPanelRef.value) {
                logPanelRef.value.removeEventListener('scroll', handleScroll);
            }

            window.removeEventListener('resize', adjustHeight);
        });

        return {
            logPanelRef,
            autoScroll,
            copyButtonText,
            logContentHeight,
            scrollToBottom,
            copyLogs,
            ClipboardIcon,
            ChevronDownIcon
        };
    },
    template: `
        <div class="px-2 sm:px-4 md:px-6 max-w-4xl mx-auto">
            <div class="w-full px-4 md:px-10 min-w-0 chat-input-container">
                <div class="flex flex-col w-full border min-w-0 overflow-hidden dev-log-panel">
                    <!-- 日志面板头部 -->
                    <div class="dev-log-header flex items-center justify-between px-3 py-2">
                        <div class="flex items-center">
                            <span class="text-sm font-medium">开发日志</span>
                            <span v-if="activeConversationId" class="text-xs opacity-60 ml-2">
                                会话ID: {{ activeConversationId }}
                            </span>
                        </div>
                        <div class="flex items-center gap-2">
                            <button
                                class="btn btn-xs btn-ghost hover:bg-base-200 transition-colors duration-200"
                                @click="copyLogs"
                                title="复制全部日志"
                            >
                                {{ copyButtonText }}
                            </button>
                            <button
                                class="btn btn-xs btn-ghost hover:bg-base-200 transition-colors duration-200"
                                @click="$emit('close')"
                                title="隐藏日志"
                            >
                                隐藏
                            </button>
                        </div>
                    </div>

                    <!-- 日志内容区域 -->
                    <div
                        ref="logPanelRef"
                        class="dev-log-content overflow-y-auto p-3"
                        :style="{ height: logContentHeight }"
                    >
                        <div v-if="!logs || (Array.isArray(logs) && logs.length === 0)" class="text-center py-4 opacity-60">
                            暂无日志信息
                        </div>
                        <div v-else-if="typeof logs === 'string'" class="log-html-content">
                            <div v-html="logs"></div>
                        </div>
                        <div v-else>
                            <div v-for="(log, index) in logs" :key="index" class="log-line mb-1">
                                {{ log }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};
