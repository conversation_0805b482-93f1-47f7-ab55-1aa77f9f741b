/**
 * 下拉菜单样式
 *
 * 定义通用下拉菜单样式，遵循 Apple/OpenAI 设计风格
 * 采用更精致、克制的配色方案
 */

/* 自定义下拉菜单 */
.custom-dropdown {
    position: relative;
    display: inline-block;
}

/* 下拉菜单 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 120px;
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    list-style: none;
    padding: 0.5rem 0;
    margin: 0.25rem 0 0;
    display: none;
    z-index: 50;
    transform-origin: top right;
    animation: menu-popup 0.1s ease-out;
}

@keyframes menu-popup {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

[data-theme="dark"] .dropdown-menu {
    background-color: #2a2a2a;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-dropdown:focus-within .dropdown-menu {
    display: block;
}

/* 下拉菜单项 */
.dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    color: inherit;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
    gap: 0.5rem; /* 添加间距，使图标和文本之间有适当的空间 */
    line-height: 1; /* 确保行高一致 */
}

.dropdown-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 图标包装器，确保图标垂直居中 */
.icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1rem;
    height: 1rem;
}

.icon-wrapper svg {
    width: 100%;
    height: 100%;
}

/* 确保下拉菜单内容有正确的背景色和层级 */
.dropdown-content {
    background-color: #ffffff !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;
}

[data-theme="dark"] .dropdown-content {
    background-color: #2a2a2a !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 特别针对侧边栏中的菜单内容 */
.menu-content {
    min-width: 120px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #ffffff !important;
    border-radius: 8px;
    transform-origin: top right;
    animation: menu-popup 0.1s ease-out;
}

[data-theme="dark"] .menu-content {
    background-color: #2a2a2a !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 确保下拉菜单项在悬停时有正确的背景色 */
.dropdown-content .menu li > a:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
}

[data-theme="dark"] .dropdown-content .menu li > a:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

/* 增加下拉菜单项的内边距 */
.dropdown-content .menu li > a {
    padding: 0.5rem 0.75rem !important;
    font-family: var(--font-family-sans);
    font-size: var(--font-size-sm);
}

/* 移动设备上的调整 */
@media (max-width: 768px) {
    .dropdown-item {
        padding: 0.625rem 0.875rem;
    }
    
    /* 确保移动设备上图标更大更易点击 */
    .icon-wrapper {
        width: 1.25rem;
        height: 1.25rem;
    }
    
    /* 确保下拉菜单在移动端上更易于点击 */
    .dropdown-content .menu li > a {
        padding: 0.625rem 0.875rem !important; /* 增加菜单项的内边距 */
    }
    
    /* 移动设备上确保下拉菜单背景完全不透明 */
    .conversation-item .dropdown-content {
        opacity: 1 !important;
    }
}
