/**
 * 按钮样式
 *
 * 定义通用按钮样式，遵循 Apple/OpenAI 设计风格
 * 采用更精致、克制的配色方案
 */

/* 图标按钮 */
.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background-color: transparent;
    color: inherit;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-icon:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 新建聊天按钮 */
.btn-new-chat {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 2.5rem;
    padding: 0 0.75rem;
    border-radius: 0.5rem;
    background-color: transparent;
    color: inherit;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-weight: 500;
}

.btn-new-chat:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .btn-new-chat:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 重试按钮 */
.btn-retry {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 2rem;
    padding: 0 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid currentColor;
    background-color: transparent;
    color: inherit;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.btn-retry:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .btn-retry:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 菜单按钮 */
.menu-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 50%;
    background-color: transparent;
    color: inherit;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
    padding: 0;
}

.menu-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .menu-button:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 移除三点菜单按钮的悬停效果 */
.menu-button:hover {
    background-color: transparent !important;
    transform: none !important;
}

/* 移动设备上的按钮调整 */
@media (max-width: 768px) {
    .menu-button {
        padding: 0.375rem;
        width: 2rem;
        height: 2rem;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: currentColor;
    animation: spin 1s ease-in-out infinite;
}