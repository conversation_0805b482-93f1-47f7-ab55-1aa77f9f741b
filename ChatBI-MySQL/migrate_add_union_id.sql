-- 数据库迁移脚本：为用户表添加union_id字段
-- 执行时间：需要在应用重启前执行

USE chatbi;

-- 添加union_id字段到用户表
ALTER TABLE `user` 
ADD COLUMN `union_id` varchar(64) DEFAULT NULL COMMENT '飞书用户union_id，用于获取API token' 
AFTER `open_id`;

-- 添加索引以提高查询性能
ALTER TABLE `user` 
ADD KEY `idx_union_id` (`union_id`);

-- 验证字段添加成功
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'chatbi' 
AND TABLE_NAME = 'user' 
AND COLUMN_NAME = 'union_id';