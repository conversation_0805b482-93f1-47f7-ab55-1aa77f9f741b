# ChatBI-MySQL (Python) 代理指南

## 开发命令
- `python app.py`: 运行Flask应用
- `pytest tests/`: 运行所有测试
- `pytest tests/<test_file>.py::<test_function>`: 运行单个测试
- `ruff check src/`: 运行linter
- `mypy src/`: 运行类型检查

## 代码风格指南
- **核心原则**：
  - 单一职责：每个函数/类只负责一件事
  - 命名清晰：变量名、函数名要直观易懂
  - 保持简洁：函数不超过60行，避免深层嵌套
  - 避免硬编码：将常量提取为配置
  - 使用DDD：API层 → 领域服务 → 仓储层 → 数据库
  - 文档：
    - 每次重大的调整后，更新README.md
    - 每个函数/类添加docstring
    - 重要逻辑添加内联注释
    - 重大重构或者架构升级，请在 ./docs/ 目录下添加文档特别说明

- **代码结构**：
  - 函数参数不超过5个，多则用对象封装
  - 优先使用纯函数，减少副作用
  - 统一错误处理方式

- **具体要求**：
  - 使用有意义的变量名：`user_data` 而不是 `data`
  - 函数名动词开头：`get_user_info()` 而不是 `user_info()`
  - 常量全大写：`MAX_RETRY_COUNT = 3`
  - 及时处理异常，避免程序崩溃

## Git 实践
- Commit messages: 遵循规范的GitHub消息规范，说明当前改动内容和主要功能
- Branch naming: `feat/<feature-name>` 或 `fix/<issue-name>`

🤖 生成于 [opencode](https://opencode.ai)