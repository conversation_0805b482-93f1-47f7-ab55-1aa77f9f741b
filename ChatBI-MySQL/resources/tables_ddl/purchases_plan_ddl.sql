CREATE TABLE `purchases_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `purchase_no` varchar(30) DEFAULT NULL COMMENT '采购单号，关联采购单主表:purchases.purchase_no',
  `sku` varchar(30) DEFAULT NULL COMMENT '产品编号（SKU编码），关联商品SKU表:inventory.sku',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '采购总价，如果取单价话需要拿采购总价除以采购数量',
  `quantity` int(11) DEFAULT NULL COMMENT '采购数量',
  `supplier_id` int(11) DEFAULT NULL COMMENT '供应商ID，关联供应商表:supplier.id',
  `quality_date` date DEFAULT NULL COMMENT '货品保质期',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `settle_flag` int(11) DEFAULT '0' COMMENT '采购项结算标识：0-可结算, 1-不可结算（结算中或已完成）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `plan_status` int(11) DEFAULT '1' COMMENT '计划状态：0-作废数据, 1-有效数据',
  `arrange_quantity` int(10) unsigned DEFAULT '0' COMMENT '可预约数量',
  `price_type` tinyint(4) DEFAULT '0' COMMENT '价格类型：0-指定价, 1-报价单, 2-询竞价',
  `tax_rate` decimal(10,2) DEFAULT NULL COMMENT '税率',
  `pop_feature` text COMMENT 'POP拓展字段',
  PRIMARY KEY (`id`),
  KEY `purchases_plan_suppiler_index` (`supplier_id`),
  KEY `no_index` (`purchase_no`,`sku`,`production_date`),
  KEY `idx_origin_id_purchase_no` (`origin_id`,`purchase_no`),
  KEY `idx_sku` (`sku`,`purchase_no`),
  KEY `idx_arrival_date` (`latest_arrival_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购单详单表，记录采购单中每个SKU的详细信息，';
