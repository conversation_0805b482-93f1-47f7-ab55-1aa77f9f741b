CREATE TABLE `follow_up_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `m_id` bigint(30) DEFAULT NULL COMMENT '商户ID，唯一标识一个商户',
  `admin_id` int(11) DEFAULT NULL COMMENT '所属的销售员ID(`admin`.`admin_id`，或者`crm_bd_org`.`bd_id`)，标识当前负责跟进该商户的销售员',
  `admin_name` varchar(255) DEFAULT NULL COMMENT '当reassign=0时，代表当前门店所属的销售员姓名(`admin`.`realname`，或者`crm_bd_org`.`bd_name`)。否则代表该商户曾经被跟进过的销售员姓名（历史记录）',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间，记录该商户被分配给销售员的时间',
  `reassign` tinyint(1) DEFAULT '0' COMMENT '公私海标识，0表示私海客户，1表示公海客户',
  `reassign_time` datetime DEFAULT NULL COMMENT '重新指派时间，记录商户被重新指派的时间',
  `reason` varchar(100) DEFAULT NULL COMMENT '释放或跟进原因，描述为何释放或跟进该商户，比如‘新注册用户’，‘主管分配’',
  `follow_type` int(11) DEFAULT '0' COMMENT '新购买标签：0表示无，1表示新购买，2表示由定时任务处理取消新购买标签',
  `danger_day` int(11) DEFAULT NULL COMMENT '自动释放倒计时，表示距离自动释放的天数',
  `timing_follow_type` int(11) DEFAULT '0' COMMENT '省心送标签：0表示商户无未履约完的省心送订单，1表示有待履约的省心送订单',
  `province` varchar(30) DEFAULT NULL COMMENT '商户所在省份，如：安徽',
  `city` varchar(30) DEFAULT NULL COMMENT '商户所在城市，如：合肥市',
  `area` varchar(30) DEFAULT NULL COMMENT '商户所在区县，如：蜀山区',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录最后修改时间',
  `source` int(11) DEFAULT NULL COMMENT '上次所属的销售ID，0表示上次在公海',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_m_id` (`m_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商户和销售关系表(私海关系)，记录商户与我司销售员之间的关联关系。一个商户最多只能关联到一个销售，即只能处于一个销售的私海，处于私海的客户的下单金额算作销售的业绩，业绩是给销售提成的最重要考核指标。但是，商户可能不属于任意销售，这种情况我们认为商户处于公海，为公海客户。公海客户算作是M1销售经理的团队客户，业绩只能算做M1销售的业绩。';