CREATE TABLE `stock_arrange_item` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `stock_arrange_id` int(11) DEFAULT NULL COMMENT '预约单ID，关联入库预约单表:stock_arrange.id',
  `sku` varchar(30) DEFAULT NULL COMMENT 'SKU编码，关联商品SKU表:inventory.sku',
  `arrival_quantity` int(11) DEFAULT '0' COMMENT '预约数量',
  `actual_quantity` int(11) DEFAULT '0' COMMENT '实到数量',
  `type` tinyint(4) DEFAULT NULL COMMENT '商品归属：0-自营（鲜沐自营）, 1-代仓（SaaS客户或者其他代仓客户）',
  `quality_time` int(11) DEFAULT NULL COMMENT '保质期时长（天数），比如：30，结合quality_time_unit来使用，如果quality_time_unit=‘天’，则表示30天',
  `quality_time_unit` varchar(10) DEFAULT NULL COMMENT '保质期时长单位，比如：天',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商ID，关联供应商表:supplier.id',
  `abnormal_quantity` int(11) DEFAULT '0' COMMENT '异常数量',
  PRIMARY KEY (`id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_supplierid_stockarrangeid` (`supplier_id`,`stock_arrange_id`),
  KEY `idx_stock_arrange_id_sku` (`stock_arrange_id`,`sku`,`arrival_quantity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购预约单明细，通过stock_arrange_id和采购预约单表关联，商品维度详单，记录采购预约单中每个SKU的详细信息，即SKU为唯一键';
