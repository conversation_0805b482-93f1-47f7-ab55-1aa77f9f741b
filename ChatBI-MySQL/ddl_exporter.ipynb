{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.db.mysql_client import get_all_table_ddl\n", "import requests\n", "import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "table_statements = get_all_table_ddl()\n", "WORKING_DIR = \"resources/ddl\"\n", "OPENAI_API_KEY = os.getenv(\"XM_OPENAI_API_KEY\")\n", "OPENAI_API_BASE = os.getenv(\"OPENAI_API_BASE\")\n", "OPENAI_MODEL = os.getenv(\"OPENAI_MODEL\")\n", "\n", "print(\"my ip:\" + requests.get(\"https://ifconfig.io/ip\").text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from services.db.mysql_client import get_sample_data_of_given_table\n", "from logger import logger\n", "import pandas as pd\n", "import json\n", "from decimal import Decimal\n", "from datetime import datetime, date, timedelta\n", "\n", "# 自定义JSON序列化函数\n", "# 使用自定义的序列化函数将sample_data转换为JSON字符串\n", "def json_serializable(obj):\n", "    if isinstance(obj, Decimal):\n", "        return str(obj)\n", "    elif isinstance(obj, datetime):\n", "        return obj.isoformat()\n", "    elif isinstance(obj, date):  # 增加对date类型的处理\n", "        return obj.isoformat()\n", "    elif isinstance(obj, timed<PERSON><PERSON>):  # 增加对 timedelta 类型的处理\n", "        return str(obj)\n", "    raise TypeError(f\"Object of type {obj.__class__.__name__} is not JSON serializable\")\n", "\n", "import os\n", "\n", "if not os.path.exists(WORKING_DIR):\n", "    os.makedirs(WORKING_DIR)\n", "\n", "inserted_count = 0\n", "# Consider batch insertion if LightRAG supports it for efficiency\n", "docs_to_insert = []\n", "for table_name, ddl_text in table_statements.items():\n", "    sample_data = None\n", "    try:\n", "        \n", "\n", "        doc_text = f\"Table Name: {table_name}\\nDDL:\\n{ddl_text}\"\n", "        sample_data = get_sample_data_of_given_table(table_name)\n", "        sample_data_str = json.dumps(sample_data, default=json_serializable, indent=4, ensure_ascii=False)\n", "\n", "        doc_text += f\"\\nSample Data:\\n{sample_data_str}\"\n", "        docs_to_insert.append(\n", "            {\n", "                \"table_name\": table_name,\n", "                \"ddl_text\": ddl_text,\n", "                \"sample_data\": sample_data_str,\n", "            }\n", "        )\n", "\n", "        # write table_name, ddl_text, sample_data into {table_name}_ddl.md and save it into WORKING_DIR\n", "        with open(f\"{WORKING_DIR}/{table_name}_ddl.md\", \"w\") as f:\n", "            f.write(f\"## 表名字：{table_name}\\n\\n\")\n", "            f.write(f\"## DDL\\n\\n{ddl_text}\\n\\n\")\n", "            f.write(f\"## Sample Data\\n\\n{sample_data_str}\\n\\n\")\n", "\n", "    except Exception as insert_error:\n", "        logger.exception(\n", "            f\"Error preparing/inserting data for {table_name}: {insert_error}, sample_data:{sample_data}\"\n", "        )\n", "\n", "docs_to_insert_df = pd.DataFrame(docs_to_insert)\n", "docs_to_insert_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "import os\n", "\n", "client = OpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)\n", "\n", "OPENAI_MODEL = os.getenv(\"OPENAI_MODEL\")\n", "\n", "SYSTEM_PROMPT = \"\"\"请你根据以下表结构定义语句，以及样例数据，重新补充完整DDL语句的注释部分。\n", "\n", "请注意：\n", "1. 注释部分需要用中文描述。\n", "2. 注释部分需要详细描述表的各个字段的含义。\n", "3. 注释部分用200字来作为该表的注释描述表的主要功能。\n", "4. 结合样例数据，尽可能为每个字段提供数据举例。\n", "5. 为了避免繁杂的数据，请你把DDL语句精简，把那些和业务无关的字段和索引部分去掉。\n", "这样AI才好用来根据DDL语句编写SQL，用以回答问题。\"\"\"\n", "\n", "\n", "def refine_ddl(ddl_doc: str) -> str:\n", "    response = client.chat.completions.create(\n", "        model=OPENAI_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"prompt\",\n", "                \"content\": SYSTEM_PROMPT,\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"请你直接返回补充完整后的DDL语句，不要返回其他内容:\\n\\n{ddl_doc}\",\n", "            },\n", "        ],\n", "        stream=True,\n", "    )\n", "\n", "    # 使用生成器表达式迭代处理流式响应\n", "    # response.choices[0].delta.content 总是 None, 因此需要迭代\n", "    collected_chunks = []\n", "    for chunk in response:\n", "        if chunk.choices[0].delta.content is not None:\n", "            chunk_text = chunk.choices[0].delta.content\n", "            print(chunk_text, end=\"\")\n", "            collected_chunks.append(chunk_text)\n", "    return \"\".join(collected_chunks)\n", "\n", "\n", "merchant_tables = [\n", "    \"orders\",\n", "    \"order_item\",\n", "    \"merchant\",\n", "    \"inventory\",\n", "    # \"products\",\n", "    # \"category\",\n", "    # \"crm_bd_org\",\n", "    # \"follow_up_record\",\n", "    # \"follow_up_relation\",\n", "    \"warehouse_storage_center\",\n", "    # \"warehouse_logistics_center\",\n", "    \"area\",\n", "    \"delivery_plan\",\n", "    # \"admin\",\n", "]\n", "\n", "total_docs = \"\"\n", "\n", "for table_name in merchant_tables:\n", "    with open(f\"{WORKING_DIR}/{table_name}_ddl.md\", \"r\") as f:\n", "        ddl_doc = f.read()\n", "    refined_ddl_doc = refine_ddl(ddl_doc)\n", "    with open(f\"{WORKING_DIR}/{table_name}_ddl_refined.md\", \"w\") as f:\n", "        f.write(refined_ddl_doc)\n", "        total_docs += refined_ddl_doc\n", "        total_docs += \"\\n\\n\"\n", "        \n", "# write total_docs into file:\n", "with open(f\"{WORKING_DIR}/total_docs.md\", \"w\") as f:\n", "    f.write(total_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# read from file f\"{WORKING_DIR}/total_docs.md\"\n", "total_docs = open(f\"{WORKING_DIR}/total_docs.md\", \"r\").read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "from typing_extensions import Any, Dict\n", "from openai import AsyncOpenAI\n", "\n", "# 导入 agents 库的相关组件\n", "from agents import (\n", "    Agent,\n", "    Runner,\n", "    function_tool,\n", "    set_tracing_disabled,\n", "    OpenAIChatCompletionsModel,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    Model,\n", "    RunConfig,\n", ")\n", "\n", "# 导入自定义的 MySQL 客户端函数\n", "from services.db.mysql_client import execute_sql_query\n", "from openai.types.responses import ResponseTextDeltaEvent\n", "\n", "# 假设 OPENAI_API_KEY, OPENAI_API_BASE, OPENAI_MODEL, total_docs 在之前的单元格已定义\n", "\n", "# 设置 agents 库使用的默认 OpenAI 客户端\n", "# 确保 OPENAI_API_KEY 和 OPENAI_API_BASE 已正确设置\n", "client = AsyncOpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)\n", "set_tracing_disabled(disabled=True)\n", "\n", "\n", "class CustomModelProvider(ModelProvider):\n", "    def get_model(self, model_name: str = None) -> Model:\n", "        return OpenAIChatCompletionsModel(\n", "            model=model_name or OPENAI_MODEL, openai_client=client\n", "        )\n", "\n", "\n", "CUSTOM_MODEL_PROVIDER = CustomModelProvider()\n", "\n", "\n", "# 定义一个函数工具，用于执行 MySQL 查询\n", "@function_tool\n", "async def fetch_mysql_sql_result(\n", "    sql: str, description: str\n", ") -> tuple[Dict[str, Any], str]:\n", "    \"\"\"使用SQL来查询MySQL数据库，返回查询结果(字典格式)和对查询的描述。\n", "\n", "    Args:\n", "        sql: 要执行的 SQL 查询语句。\n", "        description: 对 SQL 查询的简短描述。\n", "    \"\"\"\n", "    # 调用 mysql_client 中的函数执行查询\n", "    # 假设 execute_sql_query 返回一个适合 agent 使用的字典格式结果\n", "    return execute_sql_query(sql), description\n", "\n", "# 主异步函数\n", "async def main():\n", "    # 创建 Agent 实例\n", "    # 确保提供了模型名称和所需的工具\n", "    agent = Agent(\n", "        name=\"MySQL Expert Assistant\",  # 更具描述性的 Agent 名称\n", "        instructions=f\"你是一个MySQL数据专家，专门帮助用户查询MySQL数据库。以下是数据库的DDL语句：\\n\\n{total_docs}\\n\\n请根据用户的问题生成并执行SQL查询。请注意不要使用with子句，因为该子句已经被服务端禁止。\",\n", "        model=OPENAI_MODEL,  # 明确指定要使用的模型\n", "        tools=[\n", "            fetch_mysql_sql_result,  # 将 SQL 查询工具提供给 Agent\n", "        ],\n", "    )\n", "\n", "    # 定义用户输入的问题\n", "    user_input = \"查询注册城市为杭州市的用户，3月下单额比对2月下单额下降最大的20个门店是哪些？列出门店名称和下单金额、最后下单日、下单商品列表（聚合），按照2月下单额降序排序\"\n", "\n", "    print(f\"User Query: {user_input}\\n\")\n", "    print(\"Agent Response:\\n\")\n", "\n", "    # 使用 Runner 流式运行 Agent\n", "    result = Runner.run_streamed(\n", "        agent,\n", "        input=user_input,\n", "        run_config=RunConfig(model_provider=CUSTOM_MODEL_PROVIDER),\n", "    )\n", "\n", "    # 异步迭代处理流式事件\n", "    async for event in result.stream_events():\n", "        # 检查事件类型是否为原始响应事件，并且数据是文本增量\n", "        # 注意：'raw_response_event' 是 agents 库定义的事件类型，需要确认是否准确\n", "        # 如果 agents 库有更具体的事件类型如 'text_delta_event'，应使用那个\n", "        if event.type == \"raw_response_event\" and isinstance(\n", "            event.data, ResponseTextDeltaEvent\n", "        ):\n", "            # 打印收到的文本增量，不换行，并立即刷新输出\n", "            # 检查 delta 是否为 None，虽然理论上 isinstance 检查后不应为 None，但增加健壮性\n", "            if event.data.delta:\n", "                print(event.data.delta, end=\"\", flush=True)\n", "\n", "    print(\"\\n--- End of Response ---\")  # 标记响应结束\n", "\n", "\n", "await main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}