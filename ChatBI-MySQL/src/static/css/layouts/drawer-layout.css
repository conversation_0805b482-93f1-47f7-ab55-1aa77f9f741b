/**
 * 抽屉布局样式
 *
 * 定义移动端抽屉式侧边栏的布局和行为
 * 不依赖DaisyUI，使用原生CSS实现抽屉功能
 * 保持与原有设计一致，但提高Safari兼容性
 */

/* 抽屉容器 */
.custom-drawer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* 抽屉内容区域 */
.custom-drawer-content {
    width: 100%;
    height: 100%;
    transition: transform var(--duration-normal) ease-out;
}

/* 抽屉侧边栏 */
.custom-drawer-side {
    position: fixed;
    top: 0;
    left: 0;
    width: 16rem; /* 256px = 64 * 4 (tailwind w-64) */
    height: 100%;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform var(--duration-normal) ease-out;
}

/* 抽屉打开时的侧边栏 */
.custom-drawer.drawer-open .custom-drawer-side {
    transform: translateX(0);
}

/* 抽屉遮罩层 */
.custom-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 45; /* 提高 z-index，确保在侧边栏下方但高于其他内容 */
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--duration-normal) ease-out,
                visibility var(--duration-normal) ease-out;
    /* 确保遮罩层可以接收点击事件 */
    pointer-events: none;
}

/* 抽屉打开时的遮罩层 */
.custom-drawer.drawer-open .custom-drawer-overlay {
    opacity: 1;
    visibility: visible;
    /* 当抽屉打开时，启用点击事件 */
    pointer-events: auto;
}

/* 桌面视图下的抽屉 */
@media (min-width: 1024px) {
    .custom-drawer-mobile {
        display: none;
    }
}

/* 移动视图下的抽屉 */
@media (max-width: 1023px) {
    .custom-drawer-desktop {
        display: none;
    }
}

/* 确保Safari兼容性的额外修复 */
.custom-drawer-side {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    -webkit-transition: -webkit-transform var(--duration-normal) ease-out;
    transition: -webkit-transform var(--duration-normal) ease-out;
    transition: transform var(--duration-normal) ease-out;
    transition: transform var(--duration-normal) ease-out, -webkit-transform var(--duration-normal) ease-out;
    will-change: transform;
}

.custom-drawer.drawer-open .custom-drawer-side {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

/* 覆盖 DaisyUI drawer 组件的过渡时间 */
.drawer-side > .drawer-overlay {
    transition-duration: var(--duration-normal) !important;
}

.drawer-side {
    transition-duration: var(--duration-normal) !important;
}
