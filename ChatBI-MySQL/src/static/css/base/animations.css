/**
 * 动画效果
 * 
 * 定义应用中使用的各种动画和过渡效果
 */

/* 淡入淡出动画 - 使用CSS变量实现一致的动画时长 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity var(--duration-normal) ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* 文本淡入动画 - 用于消息内容 */
.text-fade-in {
    animation: textFadeIn var(--duration-text) ease-out;
}

@keyframes textFadeIn {
    0% {
        opacity: 0;
        transform: translateY(5px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 渐变背景动画 */
@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
