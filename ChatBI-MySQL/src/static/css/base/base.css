/**
 * 基础样式
 *
 * 提供应用的基础样式设置，包含必要的重置和默认样式
 * 遵循Apple和OpenAI的设计风格，简约而不简单
 * 注意：字体相关样式已移至font.css
 */

/* 基础样式设置 */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

/* 确保所有元素的盒模型一致 */
*, *::before, *::after {
    box-sizing: border-box;
}

/* 移除默认外边距 */
h1, h2, h3, h4, h5, h6, p, ul, ol {
    margin-top: 0;
}

/* 图片默认样式 */
img {
    max-width: 100%;
    height: auto;
}

/* 移动端适配 - 从responsive.css合并 */
@media (max-width: 640px) {
    .max-w-4xl {
        width: 100%;
        padding: 0 0.75rem;
    }

    .chat-container {
        padding: 0.75rem;
    }
}

/* 平板适配 - 从responsive.css合并 */
@media (max-width: 768px) {
    .chat-container {
        padding: 1rem;
    }
}

/* 全局滚动行为 - 统一滚动行为，详细样式见 scrollbar.css */
.overflow-auto, .overflow-y-auto, .overflow-x-auto {
    /* 使用平滑滚动 */
    scroll-behavior: smooth;
}
