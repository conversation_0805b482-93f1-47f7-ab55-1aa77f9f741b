/**
 * Markdown 内容样式
 *
 * 定义 Markdown 渲染内容的样式，遵循 Apple/OpenAI 设计风格
 * 使用font.css中定义的字体变量
 */

/* Markdown 内容基础样式 */
.markdown-content {
    line-height: var(--line-height-relaxed);
    color: var(--color-text-primary);
    transition: color var(--duration-normal);
    width: 100%;
    font-family: var(--font-family-sans);
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);
    transition: color var(--duration-normal);
}

.markdown-content h1 { font-size: var(--font-size-2xl); }
.markdown-content h2 { font-size: var(--font-size-xl); }
.markdown-content h3 { font-size: var(--font-size-lg); }
.markdown-content h4 { font-size: var(--font-size-base); }
.markdown-content h5 { font-size: var(--font-size-base); }
.markdown-content h6 { font-size: var(--font-size-sm); }

/* 段落样式 */
.markdown-content p {
    margin-bottom: 1rem;
    transition: color var(--duration-normal);
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.markdown-content ul {
    list-style-type: disc;
}

.markdown-content ol {
    list-style-type: decimal;
}

.markdown-content li {
    margin-bottom: 0.5rem;
    transition: color var(--duration-normal);
    display: list-item; /* 确保列表项显示为列表项 */
}

/* 链接样式 */
.markdown-content a {
    color: var(--color-accent-blue);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: color var(--duration-normal),
                border-color var(--duration-normal);
}

.markdown-content a:hover {
    border-bottom: 1px solid var(--color-accent-blue);
}

/* 引用样式 */
.markdown-content blockquote {
    border-left: 3px solid var(--color-border-primary);
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
    font-family: var(--font-family-serif); /* 使用衬线字体增强引用的视觉效果 */
    color: var(--color-text-secondary);
    transition: border-color var(--duration-normal),
                color var(--duration-normal);
}

/* 代码块样式 */
.markdown-content pre {
    margin-bottom: 1rem;
    background-color: var(--color-bg-tertiary);
    border: 1px solid var(--color-border-secondary);
    transition: background-color var(--duration-normal),
                border-color var(--duration-normal);
}

/* 行内代码样式 */
.markdown-content code:not(pre code) {
    background-color: var(--color-bg-tertiary);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: var(--font-family-mono);
    font-size: 0.85em; /* 稍微小一点 */
    color: var(--color-accent-indigo);
    letter-spacing: -0.01em; /* 编程字体通常需要稍微紧凑一点 */
    transition: background-color var(--duration-normal),
                color var(--duration-normal);
}

/* 表格容器样式 - 添加水平滚动支持 */
.markdown-content .table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 1rem;
}

/* 表格样式 */
.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem; /* 14px - 比正文小一点 */
}

.markdown-content th,
.markdown-content td {
    padding: 0.5rem;
    border: 1px solid var(--color-border-secondary);
    text-align: left;
}

.markdown-content th {
    background-color: var(--color-bg-secondary);
    font-weight: var(--font-weight-semibold); /* 改为更粗的字重 */
    font-size: 0.875rem;
}

/* 响应式调整 - 使用font.css中的响应式设置，这里只保留特殊调整 */
@media (max-width: 640px) {
    .markdown-content code:not(pre code) {
        padding: 0.15rem 0.3rem;
    }

    .markdown-content table {
        font-size: 0.8125rem; /* 13px - 移动端更小 */
    }
}
