/**
 * UI 组件样式
 *
 * 定义通用 UI 组件的样式，遵循 Apple/OpenAI 设计风格
 * 整合了之前分散在不同文件中的UI样式
 */

/* 毛玻璃效果 - Apple风格 */
.glass-effect {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    transition: background-color 0.3s ease,
                backdrop-filter 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease;
}

/* 暗色模式下的毛玻璃效果 - Apple风格 */
[data-theme="dark"] .glass-effect {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* 渐变背景动画 - 使用更柔和的Apple/OpenAI风格颜色 */
.bg-gradient-animated {
    background: linear-gradient(-45deg,
        #0071e3, /* Apple蓝 */
        #10a37f, /* OpenAI绿 */
        #5856d6, /* Apple紫 */
        #34c759  /* Apple绿 */
    );
    background-size: 400% 400%;
    animation: gradient 20s ease infinite;
    opacity: 0.05;
}

/* 渐变动画关键帧 */
@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 按钮样式增强 */
.btn {
    text-transform: none;
    font-weight: 500;
    transition: background-color 0.25s ease,
                border-color 0.25s ease;
}

/* 菜单项样式已移至 sidebar.css */

/* Toast通知样式 - Apple/OpenAI风格 */
.toast {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.toast .alert {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    animation: toast-appear 0.3s ease forwards;
    max-width: 320px;
}

[data-theme="dark"] .toast .alert {
    background: rgba(40, 40, 40, 0.85);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

@keyframes toast-appear {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式调整 - 从index.html内联样式移动过来 */
@media (max-width: 768px) {
    .chat-container {
        padding: 1rem;
    }
}
