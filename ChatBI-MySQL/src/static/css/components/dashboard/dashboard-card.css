/**
 * Dashboard Card Styles
 *
 * Styles for dashboard card components, following Apple/OpenAI design aesthetics
 * with clean, minimalist styling and appropriate light/dark mode support.
 * Features refined glass-like effects, subtle shadows, and smooth transitions
 * for a sophisticated, modern UI that feels pleasant to use.
 */

/* Base dashboard card styles */
.dashboard-card {
    @apply card overflow-hidden;
    border-radius: 1rem; /* Slightly less rounded corners for better integration */
    transition: box-shadow 0.3s cubic-bezier(0.16, 1, 0.3, 1),
                border-color 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.04);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06),
                0 1px 2px rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background-color: rgba(255, 255, 255, 0.7);
    position: relative;
    overflow: hidden;
}

/* Subtle gradient overlay for light theme */
.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
    z-index: 0;
}

/* Dark mode adjustments */
[data-theme="dark"] .dashboard-card {
    border-color: rgba(255, 255, 255, 0.02);
    background-color: rgba(30, 30, 35, 0.7);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05),
                0 1px 2px rgba(0, 0, 0, 0.03);
}

/* Dark mode gradient overlay */
[data-theme="dark"] .dashboard-card::before {
    background: linear-gradient(135deg, rgba(50, 50, 60, 0.3) 0%, rgba(30, 30, 35, 0) 100%);
}

/* Hover effects removed as requested */
/* .dashboard-card:hover styles removed */

/* [data-theme="dark"] .dashboard-card:hover styles removed */


/* Ensure card content is above the gradient overlay */
.dashboard-card > * {
    position: relative;
    z-index: 1;
}

/* Card body padding is now controlled by Tailwind classes in the component template */

/* Card title styles */
.dashboard-card .card-title {
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.85;
    margin-bottom: 0.25rem;
    letter-spacing: -0.01em;
}

.dashboard-card .card-subtitle {
    font-size: 0.8125rem; /* Slightly smaller subtitle */
    opacity: 0.6;
    margin-top: -0.25rem;
    margin-bottom: 0.5rem; /* Further reduced bottom margin */
    font-weight: 400;
}

/* Stats card specific styles */
.stats-card .stats-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.stats-card .stats-description {
    font-size: 0.875rem;
    opacity: 0.7;
    font-weight: 400;
}

/* Icon container for stats cards */
.stats-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    width: 2.5rem;
    height: 2.5rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    z-index: 2;
}

/* Ensure the icon is perfectly centered */
.stats-icon-container svg {
    width: 1.25rem;
    height: 1.25rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Dark mode icon container */
[data-theme="dark"] .stats-icon-container {
    background: rgba(30, 30, 35, 0.7);
    border-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Chart card specific styles */
.chart-card .chart-container {
    width: 100%;
    height: 100%;
    min-height: 220px;
    position: relative;
    overflow: hidden;
}

/* Bar chart specific styles */
.bar-chart-container {
    margin-top: 0.5rem;
    position: relative;
    max-width: 100%;
}

.bar-chart-container .bar-label {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Trend chart specific styles */
.trend-chart-container {
    margin-top: 0.5rem;
    position: relative;
}

/* Chart legends styling */
.chart-legend {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    opacity: 0.8;
}

.chart-legend-color {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.375rem;
}

/* Loading state styles */
.dashboard-card-loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.dashboard-card-loading .loading-block {
    background-color: var(--base-300, #e5e7eb);
    border-radius: 0.5rem;
}

.dashboard-card-loading .loading-title {
    height: 1rem;
    width: 33%;
    margin-bottom: 1rem;
}

.dashboard-card-loading .loading-content {
    height: 2rem;
    width: 66%;
    margin-bottom: 0.5rem;
}

.dashboard-card-loading .loading-chart {
    width: 100%;
    /* height: 10rem; removed to allow flex-grow to determine height */
    /* margin-top: 1rem; removed, handled by mt-2 in template */
}
