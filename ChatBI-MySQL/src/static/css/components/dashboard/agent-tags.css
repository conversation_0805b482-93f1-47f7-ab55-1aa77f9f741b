/**
 * Agent Tags Styles
 *
 * Fully dynamic, zero-hardcode agent tag styling system
 * Uses HSL color space with CSS custom properties for infinite scalability
 * Follows Apple/OpenAI design aesthetics with mathematical color harmony
 */

/* Base agent tag styles */
.agent-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 0.75rem;
    font-size: 0.8125rem;
    font-weight: 500;
    letter-spacing: -0.01em;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: opacity 0.2s ease;
}

/* Dynamic color system using CSS custom properties */
.agent-tag-dynamic {
    /* Light theme colors */
    background: linear-gradient(135deg,
        hsla(var(--agent-hue), var(--agent-saturation), calc(var(--agent-lightness) + 35%), 0.12) 0%,
        hsla(var(--agent-hue), var(--agent-saturation), calc(var(--agent-lightness) + 35%), 0.08) 100%);
    color: hsl(var(--agent-hue), var(--agent-saturation), calc(var(--agent-lightness) - 15%));
    border-color: hsla(var(--agent-hue), var(--agent-saturation), var(--agent-lightness), 0.2);
}

/* Agent name styling */
.agent-name {
    font-weight: 500;
    white-space: nowrap;
}

/* Dark mode adjustments for dynamic colors */
[data-theme="dark"] .agent-tag-dynamic {
    /* Enhanced contrast and brightness for dark theme */
    background: linear-gradient(135deg,
        hsla(var(--agent-hue), var(--agent-saturation), calc(var(--agent-lightness) + 20%), 0.15) 0%,
        hsla(var(--agent-hue), var(--agent-saturation), calc(var(--agent-lightness) + 20%), 0.1) 100%);
    color: hsl(var(--agent-hue), var(--agent-saturation), calc(var(--agent-lightness) + 25%));
    border-color: hsla(var(--agent-hue), var(--agent-saturation), calc(var(--agent-lightness) + 20%), 0.25);
}



/* Mobile responsiveness */
@media (max-width: 768px) {
    .agent-tag {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.625rem;
    }
}

/* Subtle animation for when tags appear */
.agent-tag {
    animation: agentTagFadeIn 0.3s ease-out;
}

@keyframes agentTagFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Multiple agents layout */
.agent-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

/* Ensure proper spacing in table cells */
td .agent-tag {
    margin: 0;
}
