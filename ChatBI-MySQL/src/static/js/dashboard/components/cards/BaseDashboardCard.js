/**
 * BaseDashboardCard Component
 *
 * Base component for all dashboard cards, extending Daisy UI's card component
 * with consistent styling, loading states, and slot structure.
 */
import { computed } from 'vue';

export default {
    name: 'BaseDashboardCard',
    props: {
        /**
         * Additional CSS classes to apply to the card
         */
        cardClass: {
            type: String,
            default: ''
        },
        /**
         * Whether the card is in a loading state
         */
        loading: {
            type: Boolean,
            default: false
        },
        /**
         * Card size (can be 'sm', 'md', 'lg', 'xl', 'auto' or a Tailwind class like 'row-span-2')
         * Use 'auto' for cards that should adapt to their content height
         */
        size: {
            type: String,
            default: 'md'
        }
    },
    setup(props, { slots }) {
        // Get size-related classes
        const getSizeClasses = (size) => {
            switch (size) {
                case 'sm':
                    return 'h-48'; // 12rem
                case 'md':
                    return 'h-64'; // 16rem
                case 'lg':
                    return 'h-96'; // 24rem
                case 'xl':
                    return 'h-128'; // 32rem
                case 'auto':
                    return ''; // No fixed height, will adapt to content
                default:
                    // If it's a Tailwind class like row-span-2, return it directly
                    return size.startsWith('row-span-') || size.startsWith('col-span-') ? size : '';
            }
        };

        // Compute combined classes for the card
        const cardClasses = computed(() => {
            const classes = [
                'dashboard-card',
                'flex', // Ensure the card itself is a flex container
                'flex-col', // Arrange children (header, body, footer) vertically
                props.loading ? 'dashboard-card-loading' : '',
                props.cardClass
            ];

            // Add size class if it's a predefined size or a Tailwind class
            const sizeClass = getSizeClasses(props.size);
            if (sizeClass) {
                classes.push(sizeClass);
            }

            return classes.filter(Boolean).join(' ');
        });

        // Compute style object for the card
        const cardStyle = computed(() => {
            const style = {};
            return style;
        });

        // Check if slots exist
        const hasHeaderSlot = computed(() => !!slots.header);
        const hasFooterSlot = computed(() => !!slots.footer);

        return {
            cardClasses,
            cardStyle,
            hasHeaderSlot,
            hasFooterSlot
        };
    },
    template: `
        <div :class="cardClasses" :style="cardStyle">
            <!-- Card Header (if slot provided) -->
            <div v-if="hasHeaderSlot" class="card-header px-5 py-3 pb-1">
                <slot name="header">
                    <!-- Default header content if needed -->
                </slot>
            </div>

            <!-- Card Body -->
            <div class="card-body px-5 pt-1 pb-3 relative flex-grow">

                <!-- Loading State -->
                <template v-if="loading">
                    <div class="absolute inset-0 flex flex-col p-1">
                        <div class="loading-block loading-title"></div>
                        <div class="loading-block loading-content mt-1.5"></div>
                        <div class="loading-block loading-chart flex-grow mt-1.5"></div>
                    </div>
                </template>

                <!-- Main Content -->
                <template v-else>
                    <slot></slot>
                </template>
            </div>

            <!-- Card Footer (if slot provided) -->
            <div v-if="hasFooterSlot" class="card-footer px-5 py-2 border-t border-base-200">
                <slot name="footer">
                    <!-- Default footer content if needed -->
                </slot>
            </div>
        </div>
    `
};
