/**
 * TrendCard Component
 *
 * Card component for displaying line charts showing trends using vue-chartjs
 * Extends BaseDashboardCard with specific styling and functionality for trend charts
 */
import { ref, onMounted, computed, onUnmounted, watch } from 'vue';
import BaseDashboardCard from './BaseDashboardCard.js';

export default {
    name: 'TrendCard',
    components: {
        BaseDashboardCard
    },
    props: {
        /**
         * Card title
         */
        title: {
            type: String,
            default: ''
        },
        /**
         * Card subtitle
         */
        subtitle: {
            type: String,
            default: ''
        },
        /**
         * Chart data in Chart.js format
         */
        chartData: {
            type: Object,
            required: true
        },
        /**
         * Line colors for datasets
         */
        lineColors: {
            type: Array,
            default: () => [
                {
                    light: 'rgb(59, 130, 246)',
                    dark: 'rgb(96, 165, 250)'
                },
                {
                    light: 'rgb(236, 72, 153)',
                    dark: 'rgb(244, 114, 182)'
                },
                {
                    light: 'rgb(16, 185, 129)',
                    dark: 'rgb(52, 211, 153)'
                },
                {
                    light: 'rgb(245, 158, 11)',
                    dark: 'rgb(251, 191, 36)'
                }
            ]
        },
        /**
         * Whether to enable Chart.js animations
         */
        enableAnimation: {
            type: Boolean,
            default: true
        },
        /**
         * Chart.js options to override defaults
         */
        chartOptions: {
            type: Object,
            default: () => ({})
        },
        /**
         * Whether the card is in a loading state
         */
        loading: {
            type: Boolean,
            default: false
        },
        /**
         * Additional CSS classes for the card
         */
        cardClass: {
            type: String,
            default: ''
        },
        /**
         * Card size (can be 'sm', 'md', 'lg', 'xl' or a Tailwind class like 'row-span-2')
         */
        size: {
            type: String,
            default: 'lg'
        }
    },
    setup(props) {
        // Reference to the chart canvas element
        const chartCanvas = ref(null);
        // Reference to the Chart.js instance
        const chartInstance = ref(null);
        // Track current theme
        const isDarkTheme = ref(document.documentElement.getAttribute('data-theme') === 'dark');
        // Track if Chart.js is loaded
        const chartJsLoaded = ref(!!window.Chart);

        // Combine classes for the trend card
        const combinedCardClass = computed(() => {
            return `chart-card trend-card shadow-sm ${props.cardClass}`;
        });

        // Flag to track if chart is being reinitialized
        const isReinitializing = ref(false);

        // Initialize and render the chart
        const initChart = () => {
            if (!chartCanvas.value) return;
            if (isReinitializing.value) return;


            // Ensure Chart.js is loaded
            if (!window.Chart) {
                console.error('[TrendCard] Chart.js is not loaded');
                chartJsLoaded.value = false;
                return;
            }

            try {
                isReinitializing.value = true;

                if (chartInstance.value) {
                    try {
                        chartInstance.value.destroy();
                    } catch (e) {
                        console.warn('[TrendCard] Error destroying chart:', e);
                    }
                    // Give time for chart to be properly destroyed
                    chartInstance.value = null;
                }

                // Create a copy of the chart data
                const chartDataCopy = {
                    labels: [...props.chartData.labels || []],
                    datasets: props.chartData.datasets ? props.chartData.datasets.map((dataset, index) => {
                        // Get color for this dataset
                        const colorSet = props.lineColors[index % props.lineColors.length];
                        const color = isDarkTheme.value ? colorSet.dark : colorSet.light;

                        // Create a modified dataset with proper styling
                        return {
                            ...dataset,
                            borderColor: dataset.borderColor || color,
                            backgroundColor: color.replace('rgb(', 'rgba(').replace(')', `, ${21/255})`), // 0x15/255 (approx 8.2%) opacity
                            tension: dataset.tension !== undefined ? dataset.tension : 0.3,
                            fill: dataset.fill !== undefined ? dataset.fill : true,
                            pointBackgroundColor: dataset.pointBackgroundColor || color,
                            pointBorderColor: dataset.pointBorderColor || (isDarkTheme.value ? '#1D232A' : '#ffffff'),
                            pointRadius: dataset.pointRadius !== undefined ? dataset.pointRadius : 3,
                            pointHoverRadius: dataset.pointHoverRadius !== undefined ? dataset.pointHoverRadius : 5,
                            borderWidth: dataset.borderWidth !== undefined ? dataset.borderWidth : 2,
                            pointBorderWidth: 1.5,
                            pointShadowBlur: 5,
                            pointShadowOffsetX: 0,
                            pointShadowOffsetY: 2,
                            pointShadowColor: color.replace('rgb(', 'rgba(').replace(')', `, ${80/255})`) // 0x50/255 (approx 31.4%) opacity
                        };
                    }) : []
                };

                // Default chart options
                const defaultOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: props.enableAnimation
                        ? {
                            duration: 1200,
                            easing: 'easeInOutQuad',
                            onComplete: function() {
                                // Reset reinitialization flag when animation completes
                                setTimeout(() => {
                                    isReinitializing.value = false;
                                }, 50);
                            }
                          }
                        : { duration: 0 },
                        onComplete: function() {
                                isReinitializing.value = false;
                                // Reset reinitialization flag when animation completes
                                setTimeout(() => {
                                    isReinitializing.value = false;
                                }, 50);
                        },
                    plugins: {
                        legend: {
                            position: 'top',
                            align: 'end',
                            labels: {
                                boxWidth: 10,
                                boxHeight: 10,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                padding: 16,
                                font: {
                                    family: 'Inter, system-ui, sans-serif',
                                    size: 11
                                },
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.75)' : 'rgba(0, 0, 0, 0.75)'
                            }
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            backgroundColor: isDarkTheme.value ? 'rgba(30, 30, 35, 0.95)' : 'rgba(255, 255, 255, 0.95)',
                            titleColor: isDarkTheme.value ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)',
                            bodyColor: isDarkTheme.value ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.8)',
                            borderColor: isDarkTheme.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8,
                            titleFont: {
                                family: 'Inter, system-ui, sans-serif',
                                weight: '600',
                                size: 12
                            },
                            bodyFont: {
                                family: 'Inter, system-ui, sans-serif',
                                size: 11
                            },
                            boxPadding: 4,
                            usePointStyle: true,
                            callbacks: {
                                labelPointStyle: function() {
                                    return {
                                        pointStyle: 'circle',
                                        rotation: 0
                                    };
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            grid: {
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false, // Remove x-axis border line
                                lineWidth: 0.5
                            },
                            ticks: {
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)',
                                padding: 8,
                                font: {
                                    family: 'Inter, system-ui, sans-serif',
                                    size: 10
                                },
                                maxRotation: 0,
                                minRotation: 0,
                                autoSkip: true,
                                maxTicksLimit: 7 // Limit ticks on x-axis for clarity
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false, // Remove y-axis border line
                                lineWidth: 0.5,
                                // drawTicks: false, // Remove y-axis ticks if not needed for design
                            },
                            ticks: {
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)',
                                padding: 8,
                                font: {
                                    family: 'Inter, system-ui, sans-serif',
                                    size: 10
                                },
                                // precision: 0, // Ensure whole numbers if applicable
                                callback: function(value) {
                                    if (value >= 1000000) return (value / 1000000) + 'M';
                                    if (value >= 1000) return (value / 1000) + 'K';
                                    return value;
                                }
                            }
                        }
                    }
                };

                // Create new chart instance
                chartInstance.value = new Chart(chartCanvas.value, {
                    type: 'line',
                    data: chartDataCopy,
                    options: { ...defaultOptions, ...props.chartOptions }
                });
            } catch (error) {
                console.error('[TrendCard] Error initializing chart:', error);
            }
        };

        // Watch for theme changes
        const themeObserver = new MutationObserver((mutationsList) => {
            for (const mutation of mutationsList) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    isDarkTheme.value = document.documentElement.getAttribute('data-theme') === 'dark';
                    if (!props.loading && !isReinitializing.value) {
                        // Use a longer delay for theme changes to ensure any previous animations are complete
                        setTimeout(() => {
                            if (!isReinitializing.value) {
                                initChart();
                            }
                        }, 200);
                    }
                }
            }
        });

        // Watch for changes in chart data
        watch(() => [props.chartData, props.loading], () => {
            if (!props.loading && chartJsLoaded.value) {
                setTimeout(() => {
                    initChart();
                }, 100);
            }
        }, { deep: true });

        // Start observing theme changes
        onMounted(() => {
            chartJsLoaded.value = !!window.Chart;
            if (!chartJsLoaded.value) {
                console.warn('[TrendCard] Chart.js not found. Charts will not be displayed.');
            } else if (!props.loading) {
                setTimeout(() => {
                    initChart();
                }, 100);
            }
            themeObserver.observe(document.documentElement, { attributes: true });
        });

        // Clean up observer and chart instance
        onUnmounted(() => {
            if (chartInstance.value) {
                chartInstance.value.destroy();
            }
            themeObserver.disconnect();
        });

        return {
            chartCanvas,
            chartInstance,
            combinedCardClass,
            chartJsLoaded
        };
    },
    template: `
        <BaseDashboardCard
            :loading="loading"
            :card-class="combinedCardClass"
            :size="size"
        >
            <template #header>
                <div class="card-title">{{ title }}</div>
                <div v-if="subtitle" class="card-subtitle">{{ subtitle }}</div>
            </template>
            <template #default>
                <div v-if="!loading && !chartJsLoaded" class="flex items-center justify-center h-full text-center text-error p-4">
                    Chart.js library is not loaded. Please ensure it is included in your project.
                </div>
                <div v-else class="chart-container">
                    <canvas ref="chartCanvas"></canvas>
                </div>
            </template>
        </BaseDashboardCard>
    `
};
