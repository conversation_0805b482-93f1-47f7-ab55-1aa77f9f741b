import BaseHeader from '../../common/components/BaseHeader.js';
import { computed } from 'vue';

export default {
    name: 'DashboardHeader',
    components: {
        BaseHeader
    },
    props: {
        userInfo: {
            type: Object,
            required: true
        },
        isDarkTheme: {
            type: Boolean,
            default: false
        }
    },
    emits: ['toggleTheme'],
    setup(props) {
        // Create a computed property for the title with gradient styling
        const dashboardTitle = computed(() => {
            return 'ChatBI <span class="dashboard-gradient">Dashboard</span>';
        });

        return {
            dashboardTitle
        };
    },
    template: `
        <div class="relative">
            <BaseHeader
                :user-info="userInfo"
                :is-dark-theme="isDarkTheme"
                :is-dashboard="true"
                :home-url="'/dashboard'"
                :title="dashboardTitle"
                @toggle-theme="$emit('toggleTheme', $event)"
            >
            </BaseHeader>
        </div>
    `
};
