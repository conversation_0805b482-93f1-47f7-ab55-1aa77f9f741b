/**
 * GroupSection Component
 *
 * A layout component for dashboard sections with consistent styling
 * Provides slots for title area (with optional filters) and main content
 * Can be used with data-group attribute for anchor points
 */
import { computed } from 'vue';
import { zLayoutHeader } from '../../utils/zIndex.js';

export default {
    name: 'GroupSection',
    props: {
        /**
         * Optional group ID for anchor links
         */
        groupId: {
            type: String,
            default: ''
        },
        /**
         * Optional margin bottom spacing
         */
        marginBottom: {
            type: String,
            default: 'mb-10'
        }
    },
    setup(props) {
        // Compute data-group attribute if groupId is provided
        const dataGroupAttr = computed(() => {
            return props.groupId ? { 'data-group': props.groupId } : {};
        });

        return {
            dataGroupAttr,
            zLayoutHeader
        };
    },
    template: `
        <section :class="marginBottom" v-bind="dataGroupAttr">
            <!-- Title area with optional filters on the right -->
            <div class="flex flex-col gap-3 mb-5 group-section-title" v-if="$slots.title">
                <slot name="title"></slot>
            </div>

            <!-- Main content area -->
            <slot></slot>
        </section>
    `
};
