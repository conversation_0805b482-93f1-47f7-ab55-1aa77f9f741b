/**
 * Dashboard Service
 *
 * Provides API services for dashboard data
 * Centralizes all API calls for better separation of concerns
 */

/**
 * Convert date range to timestamp range
 * @param {string} range - Date range identifier ('today', 'week', 'month', 'all')
 * @returns {Object} Object with startTime and endTime
 */
const getTimeRange = (range) => {
    const now = new Date();
    let startTime = null;

    switch(range) {
        case 'today':
            startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
            break;
        case 'week':
            startTime = new Date(now);
            startTime.setDate(now.getDate() - 7);
            break;
        case 'month':
            startTime = new Date(now);
            startTime.setDate(now.getDate() - 30);
            break;
        case 'all':
        default:
            startTime = null;
    }

    return {
        startTime: startTime ? startTime.getTime() : null,
        endTime: now.getTime()
    };
};

/**
 * Convert date string to timestamp
 * @param {string} dateStr - Date string in YYYY-MM-DD format
 * @param {boolean} isEndDate - Whether this is an end date (should be set to end of day)
 * @returns {number} Timestamp in milliseconds
 */
const dateToTimestamp = (dateStr, isEndDate = false) => {
    if (!dateStr) return null;

    const date = new Date(dateStr);

    // For end date, set time to 23:59:59
    if (isEndDate) {
        date.setHours(23, 59, 59, 999);
    } else {
        date.setHours(0, 0, 0, 0);
    }

    return date.getTime();
};

/**
 * Fetch dashboard statistics
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @param {boolean} filterAdmin - Whether to filter out admin users
 * @returns {Promise<Object>} Dashboard statistics
 */
export const fetchDashboardStats = async (startDate, endDate, filterAdmin = false) => {
    try {
        const params = new URLSearchParams();

        // Convert dates to timestamps
        const startTime = dateToTimestamp(startDate);
        const endTime = dateToTimestamp(endDate, true);

        if (startTime) params.append('start_time', startTime);
        if (endTime) params.append('end_time', endTime);
        params.append('filter_admin', filterAdmin);

        const response = await fetch(`/api/dashboard/stats?${params.toString()}`);
        if (!response.ok) throw new Error('Failed to fetch stats');
        return await response.json();
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        throw error;
    }
};

/**
 * Fetch top users data
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @param {boolean} filterAdmin - Whether to filter out admin users
 * @param {number} limit - Maximum number of users to return
 * @returns {Promise<Array>} Top users data
 */
export const fetchTopUsers = async (startDate, endDate, filterAdmin = false, limit = 5) => {
    try {
        const params = new URLSearchParams();
        params.append('limit', limit);

        // Convert dates to timestamps
        const startTime = dateToTimestamp(startDate);
        const endTime = dateToTimestamp(endDate, true);

        if (startTime) params.append('start_time', startTime);
        if (endTime) params.append('end_time', endTime);
        params.append('filter_admin', filterAdmin);

        const response = await fetch(`/api/dashboard/top_users?${params.toString()}`);
        if (!response.ok) throw new Error('Failed to fetch top users');
        const data = await response.json();
        return data.top_users;
    } catch (error) {
        console.error('Error fetching top users:', error);
        throw error;
    }
};

/**
 * Fetch top agents data
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @param {boolean} filterAdmin - Whether to filter out admin users
 * @param {number} limit - Maximum number of agents to return
 * @returns {Promise<Array>} Top agents data
 */
export const fetchTopAgents = async (startDate, endDate, filterAdmin = false, limit = 5) => {
    try {
        const params = new URLSearchParams();
        params.append('limit', limit);

        // Convert dates to timestamps
        const startTime = dateToTimestamp(startDate);
        const endTime = dateToTimestamp(endDate, true);

        if (startTime) params.append('start_time', startTime);
        if (endTime) params.append('end_time', endTime);
        params.append('filter_admin', filterAdmin);

        const response = await fetch(`/api/dashboard/top_agents?${params.toString()}`);
        if (!response.ok) throw new Error('Failed to fetch top agents');
        const data = await response.json();
        return data.top_agents;
    } catch (error) {
        console.error('Error fetching top agents:', error);
        throw error;
    }
};

/**
 * Fetch daily usage data
 * @param {number} days - Number of days to fetch data for
 * @returns {Promise<Object>} Daily usage data
 */
export const fetchDailyUsage = async (days = 7) => {
    try {
        const response = await fetch(`/api/dashboard/daily_usage?days=${days}`);
        if (!response.ok) throw new Error('Failed to fetch daily usage data');

        return await response.json();
    } catch (error) {
        console.error('Error fetching daily usage data:', error);
        throw error;
    }
};

/**
 * Fetch daily users data
 * @param {number} days - Number of days to fetch data for
 * @returns {Promise<Object>} Daily users data
 */
export const fetchDailyUsers = async (days = 7) => {
    try {
        const response = await fetch(`/api/dashboard/daily_users?days=${days}`);
        if (!response.ok) throw new Error('Failed to fetch daily users data');
        return await response.json();
    } catch (error) {
        console.error('Error fetching daily users data:', error);
        throw error;
    }
};
