/**
 * History Service
 *
 * Provides functions for interacting with the conversation history API endpoints.
 */
import { createDebouncedRequest } from '../../utils/RequestUtils.js';

/**
 * 实际执行获取历史记录的函数
 *
 * @param {number} page - 页码（从1开始）
 * @param {number} limit - 每页条数
 * @returns {Promise<Object>} - Promise解析为 { history: Object, total_count: number }
 */
async function _fetchHistoryImpl(page = 1, limit = 20) {
    try {
        // 计算偏移量
        const offset = (page - 1) * limit;

        // 发起API请求
        const response = await fetch(`/api/history?limit=${limit}&offset=${offset}`);

        // 检查是否被重定向到登录页面
        if (response.url.includes('/login')) {
            throw new Error('用户未登录，请先登录');
        }

        // 检查响应是否正常
        if (!response.ok) {
            try {
                const errorData = await response.json();
                throw new Error(errorData.error || '获取历史记录失败');
            } catch (jsonError) {
                // 如果响应不是JSON格式
                throw new Error(`获取历史记录失败: ${response.status} ${response.statusText}`);
            }
        }

        // 解析并返回数据
        const data = await response.json();

        // 获取历史记录数据
        const historyData = data.history || {};

        return {
            history: historyData,
            total_count: data.total_count || 0
        };
    } catch (error) {
        console.error('[HistoryService] 获取历史记录时出错:', error);
        throw error;
    }
}

/**
 * 使用防抖工具包装历史记录请求函数
 * 自定义请求键生成函数，基于页码和每页条数
 */
const debouncedFetchHistory = createDebouncedRequest(_fetchHistoryImpl, {
    debounceTime: 300,
    getRequestKey: (page, limit) => `history_${page}_${limit}`,
    debug: true // 启用调试日志
});

/**
 * 获取会话历史记录（带分页）
 * 使用防抖机制避免短时间内重复请求
 *
 * @param {number} page - 页码（从1开始）
 * @param {number} limit - 每页条数
 * @returns {Promise<Object>} - Promise解析为 { history: Object, total_count: number }
 */
export async function fetchHistory(page = 1, limit = 20) {
    return debouncedFetchHistory(page, limit);
}

/**
 * 获取单个对话的消息内容
 * 直接调用 /api/history?chat=xxx 接口获取单个对话
 *
 * @param {string} conversationId - 对话ID
 * @returns {Promise<Array>} - Promise解析为对话消息数组
 */
export async function fetchConversation(conversationId) {
    try {
        if (!conversationId) {
            throw new Error('对话ID不能为空');
        }

        // 发起API请求
        const response = await fetch(`/api/history?chat=${conversationId}`);

        // 检查是否被重定向到登录页面
        if (response.url.includes('/login')) {
            throw new Error('用户未登录，请先登录');
        }

        // 检查响应是否正常
        if (!response.ok) {
            try {
                const errorData = await response.json();
                throw new Error(errorData.error || '获取对话内容失败');
            } catch (jsonError) {
                // 如果响应不是JSON格式
                throw new Error(`获取对话内容失败: ${response.status} ${response.statusText}`);
            }
        }

        // 解析并返回数据
        const data = await response.json();

        // 获取对话消息数据
        return data.conversation || [];
    } catch (error) {
        console.error('[HistoryService] 获取对话内容时出错:', error);
        throw error;
    }
}

/**
 * Delete a conversation by ID
 *
 * @param {string} conversationId - The ID of the conversation to delete
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function deleteConversation(conversationId) {
    try {
        // Make API request
        const response = await fetch(`/api/history/${conversationId}`, {
            method: 'DELETE'
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '删除对话失败');
        }

        // Parse and return the data
        return await response.json();
    } catch (error) {
        console.error('删除对话时出错:', error);
        throw error;
    }
}

/**
 * Mark or unmark a conversation as a bad case
 *
 * @param {string} conversationId - The ID of the conversation to mark
 * @param {boolean} isBadCase - Whether to mark (true) or unmark (false) the conversation
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function markConversationAsBadCase(conversationId, isBadCase = true) {
    try {
        // Make API request
        const response = await fetch('/api/mark_conversation_as_bad_case', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                conversation_id: conversationId,
                is_bad_case: isBadCase
            })
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '标记不良案例失败');
        }

        // Parse and return the data
        return await response.json();
    } catch (error) {
        console.error('标记不良案例时出错:', error);
        throw error;
    }
}

/**
 * Mark or unmark a conversation as a good case
 *
 * @param {string} conversationId - The ID of the conversation to mark
 * @param {boolean} isGoodCase - Whether to mark (true) or unmark (false) the conversation
 * @returns {Promise<Object>} - Promise resolving to the API response
 */
export async function markConversationAsGoodCase(conversationId, isGoodCase = true) {
    try {
        // Make API request
        const response = await fetch('/api/mark_conversation_as_good_case', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                conversation_id: conversationId,
                is_good_case: isGoodCase
            })
        });

        // Check if response is ok
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '标记Good Case失败');
        }

        // Parse and return the data
        return await response.json();
    } catch (error) {
        console.error('标记Good Case时出错:', error);
        throw error;
    }
}

// Note: We don't need a separate function to fetch conversation messages
// as we use the history data that's already loaded in the useHistoryState composable.
