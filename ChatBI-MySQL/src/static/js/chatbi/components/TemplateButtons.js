/**
 * Template Buttons Component
 *
 * 显示预设模板按钮的组件，支持水平滚动
 * 遵循项目的Apple/OpenAI设计风格
 */

export default {
    name: 'TemplateButtons',
    props: {
        templates: {
            type: Array,
            required: true,
            default: () => []
        }
    },
    emits: ['template-click'],
    setup(props, { emit }) {
        // 处理模板点击
        const handleTemplateClick = (template) => {
            emit('template-click', template);
        };

        return {
            handleTemplateClick
        };
    },
    template: `
        <div class="template-buttons-container">
            <div class="template-buttons-scroll scrollbar-auto">
                <div class="template-buttons-content">
                    <button
                        v-for="template in templates"
                        :key="template.id"
                        @click="handleTemplateClick(template)"
                        class="template-button"
                        :title="template.content"
                    >
                        <div class="template-button-content">
                            <div class="template-button-label">{{ template.label }}</div>
                            <div class="template-button-subtitle">{{ template.subtitle }}</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    `
};
