import BaseHeader from '../../common/components/BaseHeader.js';
import { MoreVerticalIcon, ShareIcon, TrashIcon, TerminalIcon, PencilIcon, MenuIcon } from '../../utils/Icons.js';
import { zDropdownContent } from '../../utils/zIndex.js';
import { computed } from 'vue';

export default {
    name: 'ChatHeader',
    components: {
        BaseHeader
    },
    props: {
        userInfo: {
            type: Object,
            required: true
        },
        isDarkTheme: {
            type: Boolean,
            default: false
        },
        sidebarOpen: {
            type: Boolean,
            default: false
        },
        isDesktopView: {
            type: Boolean,
            default: true
        },
        activeConversationId: {
            type: String,
            default: null
        },
        isDevLogVisible: {
            type: Boolean,
            default: false
        }
    },
    emits: ['toggleTheme', 'toggleSidebar', 'newConversation', 'shareConversation', 'deleteConversation', 'toggleDevLog'],
    setup(props, { emit }) {
        // 新建对话
        const handleNewConversation = () => {
            emit('newConversation');
        };

        // 分享对话
        const handleShareConversation = (conversationId) => {
            emit('shareConversation', conversationId);
        };

        // 删除对话
        const handleDeleteConversation = (conversationId, title) => {
            emit('deleteConversation', conversationId, title);
        };

        // 计算属性：是否为"新对话"页面
        const isNewChatPage = computed(() => {
            return !props.activeConversationId;
        });

        // 计算属性：是否为开发人员
        const isDeveloper = computed(() => {
            return props.userInfo && props.userInfo.isAdmin;
        });

        // 切换开发日志显示
        const toggleDevLog = () => {
            emit('toggleDevLog');
        };

        return {
            handleNewConversation,
            handleShareConversation,
            handleDeleteConversation,
            toggleDevLog,
            isNewChatPage,
            isDeveloper,
            // Icons
            MoreVerticalIcon,
            ShareIcon,
            TrashIcon,
            TerminalIcon,
            PencilIcon,
            MenuIcon,
            // zIndex utility
            zDropdownContent
        };
    },
    template: `
        <div class="relative">
            <BaseHeader
                :user-info="userInfo"
                :is-dark-theme="isDarkTheme"
                :title="'ChatBI'"
                :home-url="'/'"
                :is-dashboard="false"
                :sidebar-open="sidebarOpen"
                :is-desktop-view="isDesktopView"
                @toggle-theme="$emit('toggleTheme', $event)"
                @toggle-sidebar="$emit('toggleSidebar')"
            >
                <template #start>
                    <div class="flex items-center gap-1">
                        <!-- 侧边栏切换按钮 -->
                        <button class="btn btn-ghost btn-square" @click="$emit('toggleSidebar')" title="切换侧边栏">
                            <span v-html="MenuIcon"></span>
                        </button>
                        <!-- 新建对话按钮 -->
                        <button class="btn btn-ghost btn-square" @click="handleNewConversation" title="新建对话">
                            <span v-html="PencilIcon"></span>
                        </button>
                    </div>
                </template>
                <template #actions>
                    <!-- 三点菜单 - 仅在非"新对话"页面显示 -->
                    <div v-if="!isNewChatPage" class="dropdown dropdown-end">
                        <div tabindex="0" role="button" class="btn btn-ghost btn-circle hover:bg-base-200 transition-colors duration-200">
                            <span v-html="MoreVerticalIcon"></span>
                        </div>
                        <ul tabindex="0" class="dropdown-content menu p-2 shadow-lg bg-base-100 rounded-box min-w-max" :style="{ zIndex: zDropdownContent }">
                            <li v-if="isDeveloper">
                                <a @click.stop="toggleDevLog" class="text-sm py-2">
                                    <span v-html="TerminalIcon" class="w-4 h-4"></span>
                                    {{ isDevLogVisible ? '隐藏' : '日志' }}
                                </a>
                            </li>
                            <li>
                                <a @click.stop="handleShareConversation(activeConversationId)" class="text-sm py-2">
                                    <span v-html="ShareIcon" class="w-4 h-4"></span>
                                    分享
                                </a>
                            </li>
                            <li>
                                <a @click.stop="handleDeleteConversation(activeConversationId, '当前对话')" class="text-sm text-error py-2">
                                    <span v-html="TrashIcon" class="w-4 h-4"></span>
                                    删除
                                </a>
                            </li>
                        </ul>
                    </div>
                </template>
            </BaseHeader>
        </div>
    `
};
