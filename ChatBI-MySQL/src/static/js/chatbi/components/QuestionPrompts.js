/**
 * Question Prompts Component
 *
 * 显示AI推荐问题提示的组件，结合了欢迎模块和模板按钮的功能
 * 遵循项目的Apple/OpenAI设计风格，提供优雅的用户体验
 */

import { computed, ref, onMounted, watch } from 'vue';

// 全局单例状态 - 确保整个应用生命周期内只请求一次
const globalRecommendationState = {
    questions: ref([]),
    isLoading: ref(false),
    error: ref(null),
    hasLoaded: ref(false),
    isRequesting: ref(false),
    requestId: ref(0)
};

export default {
    name: 'QuestionPrompts',
    props: {
        show: {
            type: Boolean,
            default: true
        },
        isHistoryTransition: {
            type: Boolean,
            default: false
        }
    },
    emits: ['question-click'],
    setup(props, { emit }) {
        // 使用全局状态
        const questions = globalRecommendationState.questions;
        const isLoading = globalRecommendationState.isLoading;
        const error = globalRecommendationState.error;
        const hasLoaded = globalRecommendationState.hasLoaded;
        const isRequesting = globalRecommendationState.isRequesting;
        const requestId = globalRecommendationState.requestId;

        // 默认问题列表（作为后备）
        const defaultQuestions = [
            {
                id: 1,
                text: '我的客户中，近1个月购买日清山茶花高筋粉的客户有哪些？'
            },
            {
                id: 2,
                text: '我的客户中，过去3个月购买了菲诺厚椰乳的有哪些，列出它们的手机号、名字、总下单金额、最后下单日'
            },
            {
                id: 3,
                text: '我的客户中，近3个月内购买过全品类商品（剔除水果）的有哪些，拉出购买的产品名字，客户名字、手机号'
            },
            {
                id: 4,
                text: '日清山茶花高筋粉 在深圳的到货时间？'
            },
            {
                id: 5,
                text: '日清山茶花高筋粉 最新的质检报告？'
            },
            {
                id: 6,
                text: '我的客户中，近半年购买过大于等于2个pb品类且自然月pb单品类金额满足200的客户有哪些？并且告诉我这些客户购买的pb产品是什么，不用罗列每笔订单金额'
            },
            {
                id: 7,
                text: '拉新统计：我的客户中，哪些是今天才首次下单的？列出它们的名字、手机号、下单金额、下单时间'
            },
            {
                id: 8,
                text: '有效拜访统计：我的团队的今日有效拜访数量，按照BD分组展示'
            }
        ];

        // 获取推荐问题
        const fetchRecommendations = async () => {
            // 生成新的请求ID
            const currentRequestId = ++requestId.value;

            // 双重检查：避免重复加载和并发请求
            if (hasLoaded.value || isRequesting.value) {
                console.log(`[请求${currentRequestId}] 跳过重复请求 - hasLoaded:`, hasLoaded.value, 'isRequesting:', isRequesting.value);
                return;
            }

            try {
                isRequesting.value = true;
                isLoading.value = true;
                error.value = null;

                console.log(`[请求${currentRequestId}] 开始获取推荐问题...`);

                const response = await fetch('/api/recommendations?count=8', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                // 检查请求是否已过期（被新请求覆盖）
                if (currentRequestId !== requestId.value) {
                    console.log(`[请求${currentRequestId}] 请求已过期，忽略响应`);
                    return;
                }

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`[请求${currentRequestId}] 推荐API响应:`, data);

                if (data.success && data.recommendations && data.recommendations.length > 0) {
                    // 将推荐结果转换为组件需要的格式
                    questions.value = data.recommendations.map((text, index) => ({
                        id: index + 1,
                        text: text
                    }));
                    console.log(`[请求${currentRequestId}] 成功加载推荐问题:`, questions.value.length, '条');
                } else {
                    // 如果没有推荐结果，使用默认问题
                    console.log(`[请求${currentRequestId}] 没有推荐结果，使用默认问题`);
                    questions.value = [...defaultQuestions];
                }

                hasLoaded.value = true;

            } catch (err) {
                // 检查请求是否已过期
                if (currentRequestId !== requestId.value) {
                    console.log(`[请求${currentRequestId}] 请求已过期，忽略错误`);
                    return;
                }

                console.error(`[请求${currentRequestId}] 获取推荐问题失败:`, err);
                error.value = err.message;
                // 出错时使用默认问题
                questions.value = [...defaultQuestions];
                hasLoaded.value = true;
            } finally {
                // 只有当前请求才能重置状态
                if (currentRequestId === requestId.value) {
                    isLoading.value = false;
                    isRequesting.value = false;
                }
            }
        };

        // 使用防抖机制的初始化逻辑
        let initTimeout = null;
        const initializeRecommendations = () => {
            // 如果已经加载过，直接返回
            if (hasLoaded.value) {
                console.log('推荐问题已加载，跳过初始化');
                return;
            }

            // 清除之前的定时器
            if (initTimeout) {
                clearTimeout(initTimeout);
            }

            // 使用短暂延迟，确保所有同步的状态更新都完成
            initTimeout = setTimeout(() => {
                if (props.show && !hasLoaded.value && !isRequesting.value) {
                    console.log('初始化推荐问题请求');
                    fetchRecommendations();
                }
            }, 10); // 10ms 的短暂延迟
        };

        // 只在组件首次挂载且需要显示时才请求
        let hasInitialized = false;

        // 监听show属性变化，当组件显示时获取推荐
        watch(() => props.show, (newShow, oldShow) => {
            console.log('show属性变化:', oldShow, '->', newShow);
            // 只在首次从false变为true，或者从未初始化过时触发
            if (newShow && (!hasInitialized || (!oldShow && newShow))) {
                hasInitialized = true;
                initializeRecommendations();
            }
        });

        // 组件挂载时获取推荐
        onMounted(() => {
            console.log('QuestionPrompts组件已挂载, show:', props.show);
            if (props.show && !hasInitialized) {
                hasInitialized = true;
                initializeRecommendations();
            }
        });

        // 处理问题点击
        const handleQuestionClick = (question) => {
            emit('question-click', question);
        };

        // 根据上下文选择过渡动画名称
        const transitionName = computed(() => {
            return props.isHistoryTransition ? 'question-list-instant' : 'question-list';
        });

        // 显示的问题列表（根据加载状态决定）
        const displayQuestions = computed(() => {
            if (isLoading.value) {
                // 加载时显示骨架屏问题
                return Array.from({ length: 6 }, (_, index) => ({
                    id: `skeleton-${index}`,
                    text: '',
                    isLoading: true
                }));
            }
            return questions.value;
        });

        return {
            questions,
            displayQuestions,
            isLoading,
            error,
            hasLoaded,
            handleQuestionClick,
            transitionName,
            initializeRecommendations
        };
    },
    template: `
        <!-- 问题列表区域 - 带过渡动画和延迟 -->
        <transition :name="transitionName" appear>
            <div v-if="show" class="question-prompts-container">
                <h1 class="question-prompts-main-title">欢迎使用ChatBI</h1>

                <!-- 加载状态下的副标题 -->
                <p v-if="isLoading" class="question-prompts-subtitle">
                    🤖 正在为您生成个性化推荐问题...
                </p>

                <!-- 正常状态下的副标题 -->
                <p v-else-if="!error" class="question-prompts-subtitle">
                    以下是为您推荐的问题，您也可以直接输入您的问题
                </p>

                <!-- 错误状态下的副标题 -->
                <p v-else class="question-prompts-subtitle">
                    推荐问题加载失败，以下是常用问题示例
                </p>

                <div class="question-prompts-list scrollbar-auto">
                    <!-- 加载状态的骨架屏 -->
                    <div
                        v-if="isLoading"
                        v-for="(skeleton, index) in displayQuestions"
                        :key="skeleton.id"
                        class="question-prompt-item question-prompt-skeleton"
                        :style="{ animationDelay: (index * 60 + 400) + 'ms' }"
                    >
                        <div class="question-prompt-skeleton-content">
                            <div class="skeleton-line skeleton-line-1"></div>
                            <div class="skeleton-line skeleton-line-2"></div>
                        </div>
                    </div>

                    <!-- 正常问题列表 -->
                    <button
                        v-else
                        v-for="(question, index) in displayQuestions"
                        :key="question.id"
                        @click="handleQuestionClick(question)"
                        class="question-prompt-item"
                        :class="{ 'question-prompt-error': error }"
                        :style="{ animationDelay: (index * 60 + 400) + 'ms' }"
                        :title="question.text"
                    >
                        <span class="question-prompt-text">{{ question.text }}</span>
                    </button>
                </div>

                <!-- 重试按钮（仅在错误时显示） -->
                <div v-if="error && !isLoading" class="question-prompts-retry">
                    <button
                        @click="() => { hasLoaded = false; initializeRecommendations(); }"
                        class="question-prompts-retry-btn"
                    >
                        🔄 重新获取推荐
                    </button>
                </div>
            </div>
        </transition>
    `
};
