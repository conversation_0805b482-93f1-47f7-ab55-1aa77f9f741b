/**
 * Share Modal Component
 *
 * A modal for displaying and copying share links
 * Styled with Apple/OpenAI-inspired aesthetics
 */
import { ref } from 'vue';
import { CheckIcon, ClipboardIcon, ExternalLinkIcon } from '../../../utils/Icons.js';

export default {
    name: 'ShareModal',
    props: {
        isOpen: {
            type: Boolean,
            required: true
        },
        shareUrl: {
            type: String,
            default: ''
        },
        isGenerating: {
            type: Boolean,
            default: false
        }
    },
    emits: ['close'],
    setup(props, { emit }) {
        const isCopied = ref(false);

        const handleClose = () => {
            emit('close');
            // Reset copied state when modal closes
            setTimeout(() => {
                isCopied.value = false;
            }, 300);
        };

        const copyToClipboard = async () => {
            if (props.shareUrl) {
                try {
                    await navigator.clipboard.writeText(props.shareUrl);
                    isCopied.value = true;

                    // Reset copied state after 2 seconds
                    setTimeout(() => {
                        isCopied.value = false;
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy: ', err);
                }
            }
        };

        const openShareLink = () => {
            if (props.shareUrl) {
                window.open(props.shareUrl, '_blank');
            }
        };

        return {
            handleClose,
            copyToClipboard,
            openShareLink,
            isCopied,
            // Icons
            CheckIcon,
            ClipboardIcon,
            ExternalLinkIcon
        };
    },
    template: `
        <dialog :open="isOpen" class="modal modal-middle">
            <div class="modal-box rounded-xl shadow-lg max-w-md bg-base-100 border border-base-200">
                <h3 class="font-medium text-lg mb-6 text-base-content/90">分享对话</h3>

                <!-- Loading state -->
                <div v-if="isGenerating" class="py-8 flex flex-col items-center">
                    <span class="loading loading-spinner loading-md mb-3"></span>
                    <p class="text-center text-base-content/70 text-sm">正在生成分享链接...</p>
                </div>

                <!-- Share link display -->
                <div v-else class="py-2">
                    <p class="mb-4 text-base-content/70 text-sm">复制以下链接分享此对话：</p>

                    <!-- Share URL input with copy button - Refined design -->
                    <div class="relative mb-6 group">
                        <input
                            type="text"
                            class="w-full px-4 py-3 rounded-lg bg-base-200/30 border border-base-300/30 font-mono text-sm pr-12 focus:outline-none focus:ring-1 focus:ring-primary/20 transition-all duration-200"
                            readonly
                            :value="shareUrl"
                        />
                        <div class="absolute right-2 top-1/2 -translate-y-1/2">
                            <button
                                class="p-2 rounded-md hover:bg-base-300/40 transition-all duration-200"
                                @click="copyToClipboard"
                                title="复制链接"
                            >
                                <span v-if="isCopied" v-html="CheckIcon" class="w-5 h-5 text-success"></span>
                                <span v-else v-html="ClipboardIcon" class="w-5 h-5 text-base-content/60"></span>
                            </button>
                        </div>
                        <div v-if="isCopied" class="absolute -top-8 right-0 bg-success/10 text-success text-xs py-1 px-3 rounded-full transition-opacity duration-200">
                            已复制到剪贴板
                        </div>
                    </div>

                    <!-- Action buttons - More refined -->
                    <div class="flex justify-end gap-3 mt-2">
                        <button
                            class="px-4 py-2 rounded-lg text-base-content/70 hover:bg-base-200/70 transition-colors duration-200 text-sm"
                            @click="handleClose"
                        >
                            关闭
                        </button>
                        <button
                            class="px-4 py-2 rounded-lg bg-primary/10 text-primary hover:bg-primary/20 transition-colors duration-200 flex items-center gap-1.5 text-sm"
                            @click="openShareLink"
                        >
                            <span v-html="ExternalLinkIcon" class="w-4 h-4"></span>
                            打开链接
                        </button>
                    </div>
                </div>
            </div>

            <!-- Modal backdrop - Slightly refined -->
            <form method="dialog" class="modal-backdrop bg-black/30 backdrop-blur-sm" @click="handleClose">
                <button class="sr-only">关闭</button>
            </form>
        </dialog>
    `
};
