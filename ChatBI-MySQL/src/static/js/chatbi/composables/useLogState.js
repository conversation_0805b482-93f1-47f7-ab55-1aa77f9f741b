/**
 * Log State Composable
 *
 * Manages the state and logic for logs in the chat functionality
 * 使用单例模式确保状态在组件间共享
 */
import { ref } from 'vue';

// 在模块级别创建状态，确保单例
const currentLogs = ref(''); // 当前日志内容，HTML格式

/**
 * 处理历史消息中的日志
 * @param {Array} messages 消息数组
 */
const processHistoryLogs = (messages) => {
    // 收集所有助手消息的日志
    let accumulatedLogs = '';
    let hasLogs = false;

    // 遍历所有助手消息，累积日志
    messages.forEach(msg => {
        if (msg.role === 'assistant' && msg.logs) {
            hasLogs = true;
            if (typeof msg.logs === 'string') {
                // 字符串类型的日志（HTML）
                accumulatedLogs += msg.logs + '<br/><br/>';
            } else if (Array.isArray(msg.logs) && msg.logs.length > 0) {
                // 数组类型的日志
                // 将数组转换为HTML，确保与queryService.js中的样式一致
                const logsHtml = msg.logs.map(log => {
                    // 检查是否已经是HTML格式
                    if (log.startsWith('<p') && log.endsWith('</p>')) {
                        return log;
                    }
                    // 使用CSS类而不是内联样式
                    return `<p class="log-line">${log.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>`;
                }).join('\n');
                accumulatedLogs += logsHtml + '<br/>';
            }
        }
    });

    // 如果有累积的日志，设置到日志面板
    if (hasLogs) {
        currentLogs.value = accumulatedLogs;
    }
};

/**
 * 更新流式消息的日志
 * @param {Object} data 消息数据
 * @param {Object} message 当前消息对象
 */
const updateStreamingLogs = (data, message) => {
    // 处理日志 - 优先使用 logsHTML，因为它包含格式化的日志
    if (data.state && data.state.logsHTML && typeof data.state.logsHTML === 'string') {
        // 检查新日志是否包含当前日志
        if (typeof currentLogs.value === 'string' && currentLogs.value.length > 0) {
            if (data.state.logsHTML.includes(currentLogs.value)) {
                // 如果新日志包含当前日志，直接替换
                currentLogs.value = data.state.logsHTML;
            } else {
                // 如果新日志不包含当前日志，追加新日志
                currentLogs.value += data.state.logsHTML;
            }
        } else {
            // 如果当前日志为空，直接设置
            currentLogs.value = data.state.logsHTML;
        }

        // 同时保存到消息对象中，使用相同的逻辑
        if (typeof message.logs === 'string' && message.logs.length > 0) {
            if (data.state.logsHTML.includes(message.logs)) {
                message.logs = data.state.logsHTML;
            } else {
                message.logs += data.state.logsHTML;
            }
        } else {
            message.logs = data.state.logsHTML;
        }
    }
    // 如果没有 logsHTML 但有 logs 数组
    else if (data.state && data.state.logs) {
        if (Array.isArray(data.state.logs)) {
            // 将日志数组转换为 HTML，确保与queryService.js中的样式一致
            const logsHtml = data.state.logs.map(log => {
                // 检查是否已经是HTML格式
                if (log.startsWith('<p') && log.endsWith('</p>')) {
                    return log;
                }
                // 使用CSS类而不是内联样式
                return `<p class="log-line">${log.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>`;
            }).join('\n');

            // 追加到当前日志
            if (typeof currentLogs.value === 'string' && currentLogs.value.length > 0) {
                currentLogs.value += logsHtml;
            } else {
                currentLogs.value = logsHtml;
            }

            // 同时保存到消息对象中
            if (Array.isArray(message.logs)) {
                // 如果已有日志是数组，合并数组
                message.logs = [...message.logs, ...data.state.logs];
            } else if (typeof message.logs === 'string' && message.logs.length > 0) {
                // 如果已有日志是字符串，追加HTML
                message.logs += logsHtml;
            } else {
                // 如果没有日志，直接设置
                message.logs = data.state.logs;
            }
        } else if (typeof data.state.logs === 'string') {
            // 追加到当前日志
            if (typeof currentLogs.value === 'string' && currentLogs.value.length > 0) {
                currentLogs.value += data.state.logs;
            } else {
                currentLogs.value = data.state.logs;
            }

            // 同时保存到消息对象中
            if (typeof message.logs === 'string' && message.logs.length > 0) {
                message.logs += data.state.logs;
            } else {
                message.logs = data.state.logs;
            }
        }
    }

    return message;
};

/**
 * 清空日志
 */
const clearLogs = () => {
    currentLogs.value = '';
};

/**
 * 使用日志状态
 * @returns {Object} 日志状态和方法
 */
export function useLogState() {
    return {
        // 状态
        currentLogs,

        // 方法
        processHistoryLogs,
        updateStreamingLogs,
        clearLogs
    };
}
