/**
 * Message Handler Composable
 *
 * 处理消息相关的逻辑，包括用户消息和AI回复的处理
 * 遵循单一职责原则，专注于消息处理
 */
import { useChatState } from './useChatState.js';
import { useScrollManager } from './useScrollManager.js';

/**
 * 使用消息处理器
 * @returns {Object} 消息处理相关的方法
 */
export function useMessageHandler() {
    // 使用聊天状态
    const chatState = useChatState();

    // 使用滚动管理器
    const scrollManager = useScrollManager();

    /**
     * 处理用户消息
     * @param {Object} message 用户消息对象
     * @returns {string} 消息ID
     */
    const handleUserMessage = (message) => {
        // 添加用户消息
        const messageId = chatState.addUserMessage(message);

        // 用户发送消息时，总是滚动到底部
        setTimeout(() => {
            // 使用智能滚动，处理内容可能继续渲染的情况
            scrollManager.smartScrollToBottom();
        }, 50);

        return messageId;
    };

    /**
     * 处理AI消息开始
     * @param {Object} message 初始消息对象
     * @returns {string} 消息ID
     */
    const handleAiMessageStart = (message) => {
        // 添加AI消息
        const messageId = chatState.addAiMessage(message);

        // 如果用户没有向上滚动，则滚动到底部
        if (!scrollManager.userHasScrolledUp.value) {
            setTimeout(() => {
                // 使用智能滚动，处理内容可能继续渲染的情况
                scrollManager.smartScrollToBottom();
            }, 50);
        }

        return messageId;
    };

    /**
     * 处理AI消息流式更新
     * @param {Object|string} data 消息数据
     */
    const handleAiMessageStream = (data) => {
        // 确保传递的是字符串或包含state属性的对象
        if (typeof data === 'string') {
            chatState.updateStreamingMessage(data);
        } else if (data && (data.fullMessage !== undefined || data.state)) {
            // 如果有fullMessage属性或state属性，则认为是有效的消息对象
            chatState.updateStreamingMessage(data);
        } else {
            console.warn('[MessageHandler] 收到的消息格式不正确:', data);
            return;
        }

        // 如果用户没有向上滚动，则滚动到底部
        // 使用防抖动方式，避免频繁滚动
        if (!scrollManager.userHasScrolledUp.value) {
            // 使用简单滚动，优化性能
            scrollManager.simpleScrollToBottom();
        }
    };

    /**
     * 处理AI消息完成
     * @param {Object} message 完整消息对象
     */
    const handleAiMessageComplete = (message) => {
        // 完成流式消息
        chatState.completeStreamingMessage(message);

        // 如果用户没有向上滚动，则滚动到底部
        if (!scrollManager.userHasScrolledUp.value) {
            // 使用智能滚动，处理内容可能继续渲染的情况
            // 这里特别重要，因为AI回复完成后可能会有代码高亮等操作
            setTimeout(() => {
                scrollManager.smartScrollToBottom();
            }, 50);
        }
    };

    return {
        handleUserMessage,
        handleAiMessageStart,
        handleAiMessageStream,
        handleAiMessageComplete,
        handleMessageError: chatState.handleMessageError
    };
}
