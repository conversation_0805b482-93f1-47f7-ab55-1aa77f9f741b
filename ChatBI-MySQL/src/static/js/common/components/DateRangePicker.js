/**
 * DateRangePicker Component
 *
 * A modern, elegant date range picker component that allows users to select
 * custom start and end dates with a calendar interface.
 * Follows Apple/OpenAI-inspired aesthetic with clean design.
 */
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { zLayoutHeader } from '../../utils/zIndex.js';

export default {
    name: 'DateRangePicker',
    props: {
        /**
         * Start date value (YYYY-MM-DD format)
         */
        startDate: {
            type: String,
            default: ''
        },
        /**
         * End date value (YYYY-MM-DD format)
         */
        endDate: {
            type: String,
            default: ''
        },
        /**
         * Label text
         */
        label: {
            type: String,
            default: '日期范围'
        },
        /**
         * Whether to show the clear button
         */
        showClear: {
            type: Boolean,
            default: true
        },
        /**
         * Additional CSS classes for the main display element
         */
        displayClass: {
            type: String,
            default: ''
        }
    },
    emits: ['update:startDate', 'update:endDate', 'clear'],
    setup(props, { emit }) {
        // State
        const isOpen = ref(false);
        const pickerRef = ref(null);
        const startDateInput = ref(props.startDate);
        const endDateInput = ref(props.endDate);
        const currentMonth = ref(new Date());
        const hoverDate = ref(null);

        // Computed properties
        const formattedDateRange = computed(() => {
            if (!props.startDate && !props.endDate) return props.placeholder;

            if (props.startDate && props.endDate) {
                return `${formatDateForDisplay(props.startDate)} - ${formatDateForDisplay(props.endDate)}`;
            }

            return props.startDate ? formatDateForDisplay(props.startDate) : formatDateForDisplay(props.endDate);
        });

        const daysInMonth = computed(() => {
            const year = currentMonth.value.getFullYear();
            const month = currentMonth.value.getMonth();
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);

            // Get day of week for first day (0 = Sunday, 1 = Monday, etc.)
            const firstDayOfWeek = firstDay.getDay();

            // Calculate days from previous month to show
            const daysFromPrevMonth = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

            // Get days from previous month
            const prevMonthDays = [];
            const prevMonth = new Date(year, month, 0);
            const prevMonthLastDay = prevMonth.getDate();

            for (let i = prevMonthLastDay - daysFromPrevMonth + 1; i <= prevMonthLastDay; i++) {
                const date = new Date(year, month - 1, i);
                prevMonthDays.push({
                    date: formatDate(date),
                    day: i,
                    isCurrentMonth: false,
                    isToday: isToday(date),
                    isSelected: isDateSelected(date),
                    isInRange: isDateInRange(date),
                    isDisabled: false
                });
            }

            // Get days from current month
            const currentMonthDays = [];
            for (let i = 1; i <= lastDay.getDate(); i++) {
                const date = new Date(year, month, i);
                currentMonthDays.push({
                    date: formatDate(date),
                    day: i,
                    isCurrentMonth: true,
                    isToday: isToday(date),
                    isSelected: isDateSelected(date),
                    isInRange: isDateInRange(date),
                    isDisabled: false
                });
            }

            // Get days from next month
            const totalDaysShown = 42; // 6 rows of 7 days
            const nextMonthDays = [];
            const daysNeeded = totalDaysShown - prevMonthDays.length - currentMonthDays.length;

            for (let i = 1; i <= daysNeeded; i++) {
                const date = new Date(year, month + 1, i);
                nextMonthDays.push({
                    date: formatDate(date),
                    day: i,
                    isCurrentMonth: false,
                    isToday: isToday(date),
                    isSelected: isDateSelected(date),
                    isInRange: isDateInRange(date),
                    isDisabled: false
                });
            }

            return [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];
        });

        const monthName = computed(() => {
            return currentMonth.value.toLocaleString('default', { month: 'long', year: 'numeric' });
        });

        // Methods
        const togglePicker = () => {
            isOpen.value = !isOpen.value;

            // If opening the picker, set current month based on selected dates or current date
            if (isOpen.value) {
                if (props.startDate) {
                    const [year, month] = props.startDate.split('-').map(Number);
                    currentMonth.value = new Date(year, month - 1, 1);
                } else if (props.endDate) {
                    const [year, month] = props.endDate.split('-').map(Number);
                    currentMonth.value = new Date(year, month - 1, 1);
                } else {
                    currentMonth.value = new Date();
                }
            }
        };

        const prevMonth = () => {
            currentMonth.value = new Date(
                currentMonth.value.getFullYear(),
                currentMonth.value.getMonth() - 1,
                1
            );
        };

        const nextMonth = () => {
            currentMonth.value = new Date(
                currentMonth.value.getFullYear(),
                currentMonth.value.getMonth() + 1,
                1
            );
        };

        const selectDate = (dateString) => {
            // If no start date is selected or both dates are selected, set start date
            if (!props.startDate || (props.startDate && props.endDate)) {
                emit('update:startDate', dateString);
                emit('update:endDate', '');
            }
            // If start date is selected but no end date, set end date
            else if (props.startDate && !props.endDate) {
                // Ensure end date is after start date
                if (dateString < props.startDate) {
                    emit('update:endDate', props.startDate);
                    emit('update:startDate', dateString);
                } else {
                    emit('update:endDate', dateString);
                }

                // Close picker after selecting range
                isOpen.value = false;
            }
        };

        const handleHover = (dateString) => {
            hoverDate.value = dateString;
        };

        const clearDates = () => {
            emit('update:startDate', '');
            emit('update:endDate', '');
            emit('clear');
        };

        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        const formatDateForDisplay = (dateString) => {
            if (!dateString) return '';
            const [year, month, day] = dateString.split('-');
            return `${month}/${day}/${year}`;
        };

        const isToday = (date) => {
            const today = new Date();
            return date.getDate() === today.getDate() &&
                   date.getMonth() === today.getMonth() &&
                   date.getFullYear() === today.getFullYear();
        };

        const isDateSelected = (date) => {
            const dateString = formatDate(date);
            return dateString === props.startDate || dateString === props.endDate;
        };

        const isDateInRange = (date) => {
            if (!props.startDate || !props.endDate) {
                // If we're in the process of selecting a range (hovering)
                if (props.startDate && hoverDate.value) {
                    const dateString = formatDate(date);
                    return (dateString > props.startDate && dateString < hoverDate.value) ||
                           (dateString < props.startDate && dateString > hoverDate.value);
                }
                return false;
            }

            const dateString = formatDate(date);
            return dateString > props.startDate && dateString < props.endDate;
        };

        // Close picker when clicking outside
        const handleClickOutside = (event) => {
            if (pickerRef.value && !pickerRef.value.contains(event.target)) {
                isOpen.value = false;
            }
        };

        // Lifecycle hooks
        onMounted(() => {
            document.addEventListener('click', handleClickOutside);
        });

        onBeforeUnmount(() => {
            document.removeEventListener('click', handleClickOutside);
        });

        // Watch for prop changes
        watch(() => props.startDate, (newVal) => {
            startDateInput.value = newVal;
        });

        watch(() => props.endDate, (newVal) => {
            endDateInput.value = newVal;
        });

        return {
            isOpen,
            pickerRef,
            startDateInput,
            endDateInput,
            currentMonth,
            hoverDate,
            formattedDateRange,
            daysInMonth,
            monthName,
            togglePicker,
            prevMonth,
            nextMonth,
            selectDate,
            handleHover,
            clearDates,
            formatDateForDisplay,
            zLayoutHeader
        };
    },
    template: `
        <div class="date-range-picker relative" ref="pickerRef">

            <!-- Input Display -->
            <label class="input input-bordered input-sm h-9 flex items-center gap-2 dark:border-base-content/20 dark:border bg-base-100 dark:bg-base-100" :class="[displayClass]" @click="togglePicker">
                <svg class="h-[1em] opacity-50" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                  <line x1="3" y1="9" x2="21" y2="9" />
                  <line x1="9" y1="3" x2="9" y2="9" />
                  <line x1="15" y1="3" x2="15" y2="9" />
                </svg>
                <input type="text" readonly class="grow text-sm placeholder:text-base-content/60" :value="formattedDateRange" placeholder="选择日期范围" />
                <button v-if="showClear && (startDate || endDate)" @click.stop="clearDates" class="btn btn-ghost btn-xs btn-circle">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </label>

            <!-- Calendar dropdown -->
            <div
                v-if="isOpen"
                class="absolute mt-1 bg-base-100 dark:bg-base-100 rounded-lg shadow-lg border border-base-300 dark:border-base-content/20 p-3 w-72 dropdown-content"
                :style="{ zIndex: zLayoutHeader + 1 }"
            >
                <!-- Calendar header -->
                <div class="flex justify-between items-center mb-4">
                    <button
                        class="btn btn-ghost btn-sm btn-circle"
                        @click.prevent="prevMonth"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <span class="text-sm font-medium">{{ monthName }}</span>
                    <button
                        class="btn btn-ghost btn-sm btn-circle"
                        @click.prevent="nextMonth"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </div>

                <!-- Weekday headers -->
                <div class="grid grid-cols-7 mb-1">
                    <div v-for="day in ['一', '二', '三', '四', '五', '六', '日']" :key="day" class="text-center text-xs font-medium text-base-content/60 py-1">
                        {{ day }}
                    </div>
                </div>

                <!-- Calendar days -->
                <div class="grid grid-cols-7 gap-1">
                    <div
                        v-for="day in daysInMonth"
                        :key="day.date"
                        class="text-center py-1 text-sm rounded-md transition-colors"
                        :class="{
                            'text-base-content/40 hover:bg-base-200/50': !day.isCurrentMonth,
                            'text-base-content hover:bg-base-200/80': day.isCurrentMonth && !day.isSelected && !day.isInRange,
                            'bg-indigo-500 text-white': day.isSelected,
                            'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300': day.isInRange,
                            'ring-2 ring-indigo-300 dark:ring-indigo-500/50': day.isToday && !day.isSelected
                        }"
                        @click="selectDate(day.date)"
                        @mouseenter="handleHover(day.date)"
                    >
                        {{ day.day }}
                    </div>
                </div>

                <!-- Selected range info -->
                <div v-if="startDate || endDate" class="mt-3 pt-2 border-t border-base-300 text-xs text-base-content/70">
                    <div v-if="startDate" class="mb-1">
                        <span class="font-medium">开始日期:</span> {{ formatDateForDisplay(startDate) }}
                    </div>
                    <div v-if="endDate">
                        <span class="font-medium">结束日期:</span> {{ formatDateForDisplay(endDate) }}
                    </div>
                </div>
            </div>
        </div>
    `
};
