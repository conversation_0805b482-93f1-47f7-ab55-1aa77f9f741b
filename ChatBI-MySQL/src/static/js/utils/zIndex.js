/**
 * Z-Index Management System
 *
 * This file defines all z-index values used in the application.
 * Following a structured approach to avoid z-index conflicts.
 *
 * Format: z<Context><Element>
 * - z: prefix for z-index values
 * - Context: the stacking context (usually component name)
 * - Element: the specific element within that context
 */

// Utils
const base = 0;
const above = 1;
const below = -1;

// Page Layout (root stacking context)
export const zLayoutContent = base;
export const zLayoutHeader = above + zLayoutContent;
export const zLayoutFooter = zLayoutContent;
export const zLayoutBackdrop = above + zLayoutHeader;
export const zLayoutSidebar = above + zLayoutBackdrop;
export const zLayoutModal = above + zLayoutSidebar;
export const zLayoutPopup = above + zLayoutModal;

// Chat Area
export const zChatContent = base;
export const zChatInput = above + zChatContent;

// Message Components
export const zMessageContent = base;
export const zMessageActions = above + zMessageContent;
export const zMessageTimestamp = zMessageActions;
export const zMessageBackground = below + zMessageContent; // For background elements that should be behind content

// Dropdown Components
export const zDropdownTrigger = base;
export const zDropdownContent = 50; // 提高z-index以确保它高于其他元素

// Tooltip Components
export const zTooltipContent = 100; // Higher value to ensure it's always on top
