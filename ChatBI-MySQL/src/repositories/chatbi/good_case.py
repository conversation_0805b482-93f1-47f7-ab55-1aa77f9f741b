"""
Good case repository module.

This module provides database operations for managing good cases.
"""

from src.utils.logger import logger
from src.db.connection import execute_db_query
from mysql.connector import <PERSON><PERSON>r


def mark_conversation_as_good_case(conversation_id: str, is_good_case: bool = True, marked_by: str = None) -> bool:
    """
    Mark or unmark a conversation as a good case using the good_case table.

    Args:
        conversation_id (str): The ID of the conversation to mark
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        marked_by (str, optional): The username of the person marking the good case. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to mark a good case")
        return False

    try:
        if is_good_case:
            # Mark as good case - insert into good_case table
            # First, verify the conversation exists
            check_sql = "SELECT COUNT(*) as count FROM chat_history WHERE conversation_id = %s LIMIT 1"
            result = execute_db_query(check_sql, (conversation_id,), fetch='one')

            if not result or result['count'] == 0:
                logger.warning(f"No chat history found for conversation {conversation_id}")
                return False

            # Insert into good_case table (or update if exists)
            insert_sql = """
                INSERT INTO good_case (conversation_id, marked_by)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE
                    marked_by = VALUES(marked_by),
                    updated_at = CURRENT_TIMESTAMP
            """
            values = (conversation_id, marked_by)
            execute_db_query(insert_sql, values, commit=True)
            logger.info(f"Successfully marked conversation {conversation_id} as good case by {marked_by}")

        else:
            # Unmark as good case - remove from good_case table
            delete_sql = "DELETE FROM good_case WHERE conversation_id = %s"
            affected_rows = execute_db_query(delete_sql, (conversation_id,), fetch='count', commit=True)
            logger.info(f"Successfully unmarked conversation {conversation_id} as good case, {affected_rows} records removed")

        return True

    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error marking conversation as good case: {e}", exc_info=True)
        return False


def get_conversation_good_case_status(conversation_id: str) -> bool:
    """
    Check if a conversation is marked as a good case using the good_case table.

    Args:
        conversation_id (str): The ID of the conversation to check

    Returns:
        bool: True if the conversation is marked as a good case, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to check good case status")
        return False

    # Query the good_case table
    sql = "SELECT id FROM good_case WHERE conversation_id = %s"
    params = [conversation_id]

    # Limit to one row since we just need to check if the record exists
    sql += " LIMIT 1"

    try:
        result = execute_db_query(sql, params, fetch='one')
        is_good_case = result is not None
        logger.debug(f"Conversation {conversation_id} good case status: {is_good_case}")
        return is_good_case

    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error checking good case status: {e}", exc_info=True)
        return False
