"""
Dashboard users repository module.

This module provides data access functions for user-related dashboard operations.
"""

from typing import List, Dict, Any, Optional
from mysql.connector import Error

from src.utils.logger import logger
from src.utils.env_utils import get_admin_users
from src.db.connection import execute_db_query

def get_unique_users() -> List[Dict[str, str]]:
    """
    Get all unique users (grouped by email and username) from MySQL.
    Returns a list in the format [{'username': '...', 'email': '...'}, ...].

    Returns:
        List[Dict[str, str]]: A list of unique users
    """
    # Select both username and email, and group by both to handle users with the same name but different emails
    sql = "SELECT DISTINCT username, email FROM chat_history ORDER BY username ASC, email ASC"
    users = []

    try:
        results = execute_db_query(sql, fetch='all')
        if results:
            users = results  # Results are already in the required dictionary list format
        logger.info(f"Got {len(users)} unique user combinations (username/email)")
        return users
    except Error as e:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting unique user list: {e}", exc_info=True)
        return []

def get_conversation_count_by_user() -> Dict[str, int]:
    """
    Get the count of unique conversations for each user (identified by email) from MySQL.
    Returns a dictionary in the format {email: count, ...}.

    Returns:
        Dict[str, int]: A dictionary mapping emails to conversation counts
    """
    # Group by email and count distinct conversations
    sql = """
        SELECT email, COUNT(DISTINCT conversation_id) as count
        FROM chat_history
        GROUP BY email
        ORDER BY email ASC
    """
    user_counts = {}

    try:
        results = execute_db_query(sql, fetch='all')
        if results:
            for row in results:
                user_counts[row['email']] = row['count']
        logger.info(f"Got conversation counts for {len(user_counts)} users")
        return user_counts
    except Error as e:
        # Error already logged
        return {}
    except Exception as e:
        logger.error(f"Unexpected error getting user conversation counts: {e}", exc_info=True)
        return {}


def get_top_users(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                 filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get the top users with the most queries within a specified time range.

    Args:
        limit (int, optional): Maximum number of users to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top users with their query and conversation counts
    """
    sql = """
        SELECT
            username,
            email,
            COUNT(CASE WHEN role = 'user' THEN 1 ELSE NULL END) AS query_count,
            COUNT(DISTINCT conversation_id) AS conversation_count
        FROM chat_history
        WHERE 1=1
    """
    params = []

    # Add time range filters if provided
    if start_time is not None:
        sql += " AND timestamp >= %s"
        params.append(start_time)
    if end_time is not None:
        sql += " AND timestamp <= %s"
        params.append(end_time)

    # Add admin filter if requested
    if filter_admin:
        # Get admin users from utility function
        admin_users = get_admin_users()

        if admin_users:
            placeholders = ','.join(['%s'] * len(admin_users))
            sql += f" AND username NOT IN ({placeholders})"
            params.extend(admin_users)

    # Group by user and order by query count
    sql += """
        GROUP BY username, email
        ORDER BY query_count DESC
        LIMIT %s
    """
    params.append(limit)

    try:
        results = execute_db_query(sql, tuple(params), fetch='all')
        if results:
            logger.info(f"Got top {len(results)} users by query count")
            return results
        else:
            logger.warning("No users found for top users query")
            return []
    except Error as e:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting top users: {e}", exc_info=True)
        return []



def get_top_agents(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                  filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get the top agents with the most conversations within a specified time range.

    支持多agent字段（逗号分隔），将每个agent单独统计。

    Args:
        limit (int, optional): Maximum number of agents to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top agents with their conversation counts
    """
    try:
        # 首先获取所有符合条件的记录
        base_sql = """
            SELECT DISTINCT agent, conversation_id
            FROM chat_history
            WHERE role = 'assistant'
                AND agent IS NOT NULL
                AND agent != ''
        """
        params = []

        # Add time range filters if provided
        if start_time is not None:
            base_sql += " AND timestamp >= %s"
            params.append(start_time)
        if end_time is not None:
            base_sql += " AND timestamp <= %s"
            params.append(end_time)

        # Add admin filter if requested
        if filter_admin:
            admin_users = get_admin_users()
            if admin_users:
                placeholders = ','.join(['%s'] * len(admin_users))
                base_sql += f" AND username NOT IN ({placeholders})"
                params.extend(admin_users)

        # 获取原始数据
        raw_results = execute_db_query(base_sql, tuple(params), fetch='all')
        if not raw_results:
            logger.warning("No assistant messages with agent field found")
            return []

        # 处理多agent记录，拆分统计
        agent_conversations = {}  # {agent_name: set(conversation_ids)}

        for row in raw_results:
            agent_str = row['agent'].strip()
            conversation_id = row['conversation_id']

            # 解析多agent字段（逗号分隔）
            agents = [a.strip() for a in agent_str.split(',') if a.strip()]

            for agent_name in agents:
                # 清理agent名称（移除_specialist后缀）
                clean_agent_name = agent_name.replace('_specialist', '')

                if clean_agent_name not in agent_conversations:
                    agent_conversations[clean_agent_name] = set()
                agent_conversations[clean_agent_name].add(conversation_id)

        # 转换为统计结果并排序
        agent_stats = [
            {
                "agent_name": agent_name,
                "conversation_count": len(conversation_ids)
            }
            for agent_name, conversation_ids in agent_conversations.items()
        ]

        # 按对话数量降序排序并限制数量
        agent_stats.sort(key=lambda x: x['conversation_count'], reverse=True)
        agent_stats = agent_stats[:limit]

        logger.info(f"Got top {len(agent_stats)} agents by conversation count (multi-agent support)")
        return agent_stats

    except Error:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting top agents: {e}", exc_info=True)
        return []
