"""
Dashboard statistics repository module.

This module provides data access functions for dashboard statistics.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from mysql.connector import Error

from src.utils.logger import logger
from src.utils.env_utils import get_admin_users
from src.db.connection import execute_db_query

def get_dashboard_stats(start_time: Optional[int] = None, end_time: Optional[int] = None,
                     filter_admin: bool = False) -> Dict[str, int]:
    """
    Get dashboard statistics from MySQL: total users, total conversations, total queries (user messages),
    and bad case count.

    Args:
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        Dict[str, int]: A dictionary containing statistics
    """
    # Note: Total queries is defined as the number of messages with role='user'
    # Bad case count is now from the bad_case table
    sql = """
        SELECT
            COUNT(DISTINCT ch.email) AS total_users,
            COUNT(DISTINCT ch.conversation_id) AS total_conversations,
            COUNT(CASE WHEN ch.role = 'user' THEN 1 ELSE NULL END) AS total_queries,
            COUNT(DISTINCT bc.conversation_id) AS bad_case_count
        FROM chat_history ch
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        WHERE 1=1
    """
    params = []

    # Add time range filters if provided
    if start_time is not None:
        sql += " AND ch.timestamp >= %s"
        params.append(start_time)
    if end_time is not None:
        sql += " AND ch.timestamp <= %s"
        params.append(end_time)

    # Add admin filter if requested
    if filter_admin:
        # Get admin users from utility function
        admin_users = get_admin_users()

        if admin_users:
            placeholders = ','.join(['%s'] * len(admin_users))
            sql += f" AND ch.username NOT IN ({placeholders})"
            params.extend(admin_users)

    default_stats = {'total_users': 0, 'total_conversations': 0, 'total_queries': 0, 'bad_case_count': 0}

    try:
        stats = execute_db_query(sql, tuple(params) if params else None, fetch='one')
        if stats:
             # Ensure all keys exist
             return {
                 'total_users': stats.get('total_users', 0),
                 'total_conversations': stats.get('total_conversations', 0),
                 'total_queries': stats.get('total_queries', 0),
                 'bad_case_count': stats.get('bad_case_count', 0)
             }
        else:
             logger.warning("Dashboard statistics query returned no results")
             return default_stats

    except Error as e:
        # Error already logged
        return default_stats  # Return default values on error
    except Exception as e:
        logger.error(f"Unexpected error querying dashboard statistics: {e}", exc_info=True)
        return default_stats

def get_daily_usage_data(days: int = 30) -> Dict[str, List[Any]]:
    """
    Get daily usage data (user query count and bad case conversation count) for the specified number of days from MySQL.

    Args:
        days (int, optional): The number of days to get data for. Defaults to 30.

    Returns:
        Dict[str, List[Any]]: A dictionary containing dates, query counts, and bad case conversation counts
    """
    results = {'dates': [], 'query_counts': [], 'bad_case_convo_counts': []}
    if days <= 0: return results  # Handle invalid days

    # Calculate start timestamp (milliseconds)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days - 1)  # Include today, so subtract days-1
    start_timestamp = int(start_date.replace(hour=0, minute=0, second=0, microsecond=0).timestamp() * 1000)

    # SQL query: Group by day and count user queries and bad case conversations
    # - query_count: Number of messages with role='user'
    # - bad_case_convo_count: Number of unique conversation IDs marked as bad cases in bad_case table
    sql = """
        SELECT
            DATE(FROM_UNIXTIME(ch.timestamp / 1000)) AS usage_date,
            COUNT(CASE WHEN ch.role = 'user' THEN 1 ELSE NULL END) AS query_count,
            COUNT(DISTINCT bc.conversation_id) AS bad_case_convo_count
        FROM chat_history ch
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
            AND DATE(FROM_UNIXTIME(ch.timestamp / 1000)) = DATE(bc.created_at)
        WHERE ch.timestamp >= %s
        GROUP BY usage_date
        ORDER BY usage_date ASC;
    """

    try:
        db_results = execute_db_query(sql, (start_timestamp,), fetch='all')

        # Fill database results into date range
        db_data = {row['usage_date'].strftime('%Y-%m-%d'): row for row in db_results} if db_results else {}

        current_date = start_date.date()  # Start iterating from date object
        end_date_date = end_date.date()
        while current_date <= end_date_date:
            date_str = current_date.strftime('%Y-%m-%d')
            results['dates'].append(date_str)
            if date_str in db_data:
                results['query_counts'].append(db_data[date_str].get('query_count', 0))
                results['bad_case_convo_counts'].append(db_data[date_str].get('bad_case_convo_count', 0))
            else:
                results['query_counts'].append(0)
                results['bad_case_convo_counts'].append(0)
            current_date += timedelta(days=1)

        logger.info(f"Got daily usage data for the past {days} days")
        return results

    except Error as e:
        # Error already logged
        return {'dates': [], 'query_counts': [], 'bad_case_convo_counts': []}  # Return empty results
    except Exception as e:
        logger.error(f"Unexpected error getting daily usage data: {e}", exc_info=True)
        return {'dates': [], 'query_counts': [], 'bad_case_convo_counts': []}

def get_daily_users_data(days: int = 30) -> Dict[str, List[Any]]:
    """
    Get daily unique user count for the specified number of days from MySQL.
    Args:
        days (int, optional): The number of days to get data for. Defaults to 30.
    Returns:
        Dict[str, List[Any]]: A dictionary containing dates and user counts
    """
    results = {'dates': [], 'user_counts': []}
    if days <= 0:
        return results
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days - 1)
    start_timestamp = int(start_date.replace(hour=0, minute=0, second=0, microsecond=0).timestamp() * 1000)
    sql = """
        SELECT
            DATE(FROM_UNIXTIME(timestamp / 1000)) AS usage_date,
            COUNT(DISTINCT email) AS user_count
        FROM chat_history
        WHERE timestamp >= %s
        GROUP BY usage_date
        ORDER BY usage_date ASC;
    """
    try:
        db_results = execute_db_query(sql, (start_timestamp,), fetch='all')
        db_data = {row['usage_date'].strftime('%Y-%m-%d'): row for row in db_results} if db_results else {}
        current_date = start_date.date()
        end_date_date = end_date.date()
        while current_date <= end_date_date:
            date_str = current_date.strftime('%Y-%m-%d')
            results['dates'].append(date_str)
            if date_str in db_data:
                results['user_counts'].append(db_data[date_str].get('user_count', 0))
            else:
                results['user_counts'].append(0)
            current_date += timedelta(days=1)
        logger.info(f"Got daily users data for the past {days} days")
        return results
    except Error as e:
        return {'dates': [], 'user_counts': []}
    except Exception as e:
        logger.error(f"Unexpected error getting daily users data: {e}", exc_info=True)
        return {'dates': [], 'user_counts': []}
