"""
Database metadata repository module.

This module provides data access functions for retrieving database metadata information
such as table schemas and sample data.
"""

import os
from typing import Dict, Any
from mysql.connector import Error

from services.xianmudb.query_service import execute_business_query
from src.utils.logger import logger
from src.db.connection import get_db_connection
from src.models.query_result import SQLQueryResult
from src.db.database_enum import Database

# Environment variables
BUSINESS_DB_NAME = os.getenv("DB_NAME", "xianmudb")

def get_table_sample_data(table_name: str, limit: int = 5) -> SQLQueryResult:
    """
    获取指定表的任意示例数据。
    首先尝试获取主键列名，然后获取主键值最大的行作为示例数据。
    如果获取主键列名失败，则退回到使用 "SELECT * FROM table_name LIMIT 5" 获取示例数据。
    """
    conn = get_db_connection(Database.BUSINESS)
    if not conn or not conn.is_connected():
        return SQLQueryResult(success=False, error="数据库不可用")

    cursor = None
    sample_data_query = None
    try:
        cursor = conn.cursor(dictionary=True)

        # 尝试获取主键列名
        primary_key_column_query = """
            SELECT COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = %s
            AND TABLE_NAME = %s
            AND CONSTRAINT_NAME = 'PRIMARY';
        """
        cursor.execute(primary_key_column_query, (BUSINESS_DB_NAME, table_name))
        primary_key_columns = cursor.fetchall()  # 获取所有主键列

        if primary_key_columns:
            primary_key_column_names = [col["COLUMN_NAME"] for col in primary_key_columns]
            # 获取主键值最大的5行作为示例数据
            order_by_clause = ", ".join(
                [f"`{col}` DESC" for col in primary_key_column_names]
            )
            sample_data_query = f"""
                SELECT *
                FROM `{table_name}`
                ORDER BY {order_by_clause}
                LIMIT {limit};
            """
        else:
            # 如果没有主键或获取主键列名失败，则使用 "SELECT * LIMIT 5"
            sample_data_query = f"""
                SELECT *
                FROM `{table_name}`
                LIMIT {limit};
            """

        return execute_business_query(sample_data_query)

    except Error as e:
        logger.exception(
            f"获取表 '{table_name}' 示例数据时发生错误: {e}, SQL:{sample_data_query}"
        )
        return {"error": f"获取示例数据时发生数据库错误: {e}"}
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()

def get_database_schema() -> Dict[str, Any]:
    """
    Get xianmudb information (DDL) for all tables in the business database.

    Returns:
        Dict[str, Any]: A dictionary containing table names and their DDL
    """
    conn = None
    cursor = None
    result = {"tables": {}}

    try:
        conn = get_db_connection(Database.BUSINESS)
        cursor = conn.cursor(dictionary=True)

        # Get all tables in the database
        cursor.execute(f"SHOW TABLES FROM {BUSINESS_DB_NAME}")
        tables = cursor.fetchall()

        # For each table, get its DDL
        for table_row in tables:
            table_name = list(table_row.values())[0]  # Get the table name from the first column
            cursor.execute(f"SHOW CREATE TABLE {BUSINESS_DB_NAME}.{table_name}")
            create_table = cursor.fetchone()
            if create_table:
                # The second column contains the CREATE TABLE statement
                create_statement = list(create_table.values())[1]
                result["tables"][table_name] = create_statement

        logger.info(f"Retrieved DDL for {len(result['tables'])} tables")
        return result
    except Error as e:
        logger.error(f"Error getting table DDL: {e}", exc_info=True)
        return {"tables": {}, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error getting table DDL: {e}", exc_info=True)
        return {"tables": {}, "error": str(e)}
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()
