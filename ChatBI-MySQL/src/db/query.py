"""
SQL query execution module.

This module provides functions for executing SQL queries against the database
and handling the results.
"""

from datetime import datetime
from typing import Union
from mysql.connector import Error

from src.utils.logger import logger
from src.db.connection import get_db_connection
from src.db.database_enum import Database
from src.models.query_result import SQLQueryResult

def execute_sql_query(sql_query: str, conn=None, database: Union[Database, str] = Database.BUSINESS) -> SQLQueryResult:
    """
    Execute a given SQL query against the MySQL database and return the results.
    Only use this for SELECT queries. For other types of queries (INSERT, UPDATE, DELETE),
    respond that you cannot perform modifications.

    Args:
        sql_query (str): The SQL query string to execute
        conn: Optional database connection to use
        database (Union[Database, str], optional): The database to connect to. Either Database.CHATBI or Database.BUSINESS. Defaults to Database.BUSINESS.

    Returns:
        SQLQueryResult: An instance of SQLQueryResult containing columns, data, error, message, and success flag
    """
    # Convert string to enum if needed
    if isinstance(database, str):
        try:
            database = Database(database)
        except ValueError:
            logger.warning(f"Invalid database string: {database}, defaulting to BUSINESS")
            database = Database.BUSINESS

    # Log which database we're connecting to
    logger.debug(f"Executing SQL query against {database}")

    conn = get_db_connection(database) if conn is None else conn
    if not conn or not conn.is_connected():
        logger.error("Database connection is not available for executing SQL.")
        return SQLQueryResult(
            columns=None,
            data=None,
            error="Database connection unavailable",
            message="Database connection unavailable",
            success=False,
        )

    cursor = None
    try:
        cursor = conn.cursor()
        logger.info(f"Executing SQL (via tool): {sql_query}")
        started_at = datetime.now()
        cursor.execute(sql_query)
        if cursor.description:
            columns = [desc[0] for desc in cursor.description]
            results = cursor.fetchall()
            # Convert results to handle potential non-serializable types (like datetime)
            data = []
            for row in results:
                data.append([str(item) if item is not None else None for item in row])
            logger.info(f"Query returned: {len(results)} rows, time taken: {datetime.now() - started_at} seconds.")
            return SQLQueryResult(
                columns=columns,
                data=data,
                error=None,
                message="Query executed successfully",
                success=True,
            )
        else:
            # Should not happen due to the SELECT check, but handle defensively
            logger.warning(
                f"Query executed but returned no description (non-SELECT?): {sql_query}"
            )
            return SQLQueryResult(
                columns=[],
                data=[],
                error="Query executed but did not return results (expected SELECT).",
                message="Query executed but did not return results (expected SELECT).",
                success=False,
            )
    except Error as e:
        msg=f"Error executing SQL query: {e} SQL: {sql_query}"
        logger.error(msg)
        return SQLQueryResult(
            columns=None,
            data=None,
            error=msg,
            message=msg,
            success=False,
        )
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()
            logger.info("Database connection closed.")
