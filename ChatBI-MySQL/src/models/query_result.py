"""
SQL query result model.

This module defines the SQLQueryResult class used throughout the application
to represent the results of SQL queries.
"""

from dataclasses import dataclass, asdict, field
import json
from typing import List, Dict, Any

@dataclass
class SQLQueryResult:
    """
    Class to represent the result of a SQL query.
    
    This class is used throughout the application to represent the results of SQL queries,
    including both business database (xianmudb) and application database (chatbi) queries.
    """
    columns: List[str] = field(default_factory=list)
    data: List[List[Any]] = field(default_factory=list)
    error: str = ""
    message: str = ""
    success: bool = False
    
    @staticmethod
    def success_result(columns: List[str], data: List[List[Any]]) -> "SQLQueryResult":
        return SQLQueryResult(
            columns=columns,
            data=data,
            error="",
            message="Query executed successfully",
            success=True,
        )
    
    @staticmethod
    def failed_result(error: str) -> "SQLQueryResult":
        return SQLQueryResult(
            columns=[],
            data=[],
            error=error,
            message=error,
            success=False,
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the result to a dictionary.
        
        Returns:
            Dict[str, Any]: A dictionary representation of the result
        """
        return asdict(self)

    def to_json(self) -> str:
        """
        Convert the result to a JSON string.
        
        Returns:
            str: A JSON string representation of the result
        """
        return json.dumps(self.to_dict())
    
    def get_formatted_data(self) -> List[Dict[str, Any]]:
        """
        Get the data as a list of dictionaries, where each dictionary
        represents a row with column names as keys.
        
        Returns:
            List[Dict[str, Any]]: The formatted data
        """
        if not self.success or not self.columns or not self.data:
            return []
        
        result = []
        for row in self.data:
            row_dict = {}
            for i, col in enumerate(self.columns):
                row_dict[col] = row[i] if i < len(row) else None
            result.append(row_dict)
        
        return result
