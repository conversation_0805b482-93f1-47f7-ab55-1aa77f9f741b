"""
对话相关的通用工具函数
重构后的高内聚设计，消除重复代码，提供统一的消息处理接口
"""
from typing import Optional, List, Dict, Any, Callable
from enum import Enum
import datetime
from src.utils.logger import logger


class MessageRole(Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"


class ConversationMessageProcessor:
    """对话消息处理器 - 高内聚的消息处理类"""

    def __init__(self, conversation_id: str):
        """
        初始化消息处理器

        Args:
            conversation_id (str): 对话ID
        """
        self.conversation_id = conversation_id
        self._messages: Optional[List[Dict[str, Any]]] = None

    def _load_messages(self) -> List[Dict[str, Any]]:
        """
        懒加载对话消息

        Returns:
            List[Dict[str, Any]]: 消息列表
        """
        if self._messages is None:
            try:
                from src.repositories.chatbi.history import load_conversation
                self._messages = load_conversation(self.conversation_id) or []

                if not self._messages:
                    logger.warning(f"对话 {self.conversation_id} 没有消息历史")

            except Exception as e:
                logger.error(f"加载对话 {self.conversation_id} 消息时发生异常: {str(e)}", exc_info=True)
                self._messages = []

        return self._messages

    @staticmethod
    def _format_timestamp(timestamp: Any) -> str:
        """
        格式化时间戳为 HH:MM 格式

        Args:
            timestamp: 时间戳（毫秒）

        Returns:
            str: 格式化后的时间字符串
        """
        if timestamp and isinstance(timestamp, int):
            try:
                return datetime.datetime.fromtimestamp(timestamp / 1000).strftime('%H:%M')
            except Exception:
                pass
        return "--:--"

    @staticmethod
    def _format_message(content: str, role: MessageRole, timestamp: Any) -> str:
        """
        格式化单条消息

        Args:
            content (str): 消息内容
            role (MessageRole): 消息角色
            timestamp: 时间戳

        Returns:
            str: 格式化后的消息字符串
        """
        if not content.strip():
            return ""

        time_str = ConversationMessageProcessor._format_timestamp(timestamp)
        role_name = "用户" if role == MessageRole.USER else "助手"
        return f"[{time_str}] {role_name}：{content.strip()}"

    def _filter_messages_by_role(self, role: MessageRole) -> List[Dict[str, Any]]:
        """
        按角色过滤消息

        Args:
            role (MessageRole): 消息角色

        Returns:
            List[Dict[str, Any]]: 过滤后的消息列表
        """
        messages = self._load_messages()
        return [msg for msg in messages if msg.get('role') == role.value]

    def get_messages_by_role(self, role: MessageRole, formatter: Optional[Callable] = None) -> List[str]:
        """
        获取指定角色的所有消息

        Args:
            role (MessageRole): 消息角色
            formatter (Optional[Callable]): 自定义格式化函数

        Returns:
            List[str]: 格式化后的消息列表
        """
        messages = self._filter_messages_by_role(role)
        formatted_messages = []

        for message in messages:
            content = message.get('content', '').strip()
            if content:
                if formatter:
                    formatted_msg = formatter(message)
                else:
                    formatted_msg = self._format_message(
                        content, role, message.get('timestamp')
                    )

                if formatted_msg:
                    formatted_messages.append(formatted_msg)

        return formatted_messages

    def get_last_message_by_role(self, role: MessageRole) -> Optional[str]:
        """
        获取指定角色的最后一条消息

        Args:
            role (MessageRole): 消息角色

        Returns:
            Optional[str]: 格式化后的最后一条消息，若无消息则返回None
        """
        messages = self._load_messages()

        # 从后往前查找指定角色的最后一条消息
        for message in reversed(messages):
            if message.get('role') == role.value:
                content = message.get('content', '').strip()
                if content:
                    formatted_msg = self._format_message(
                        content, role, message.get('timestamp')
                    )
                    if formatted_msg:
                        logger.info(f"获取到最后一条{role.value}消息，长度: {len(formatted_msg)}")
                        return formatted_msg

        logger.warning(f"对话 {self.conversation_id} 中未找到{role.value}消息")
        return None


# 保持向后兼容的公共接口函数
def get_all_user_messages_content(conversation_id: str) -> Optional[str]:
    """
    获取对话中所有用户消息内容，并格式化为 [HH:MM] 用户：内容

    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[str]: 所有用户消息内容拼接后的字符串，若无用户消息则返回None
    """
    try:
        processor = ConversationMessageProcessor(conversation_id)
        user_messages = processor.get_messages_by_role(MessageRole.USER)

        if user_messages:
            logger.info(f"获取到所有用户消息内容，共{len(user_messages)}条，总长度: {sum(len(msg) for msg in user_messages)}")
            return "\n".join(user_messages)
        else:
            logger.warning(f"对话 {conversation_id} 中未找到用户消息")
            return None

    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 所有用户消息时发生异常: {str(e)}", exc_info=True)
        return None


def get_last_assistant_message_content(conversation_id: str) -> Optional[str]:
    """
    获取对话中最后一条assistant回复的内容

    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[str]: 最后一条assistant回复的内容，若无assistant回复则返回None
    """
    try:
        processor = ConversationMessageProcessor(conversation_id)
        return processor.get_last_message_by_role(MessageRole.ASSISTANT)

    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 最后一条assistant回复时发生异常: {str(e)}", exc_info=True)
        return None


# 扩展功能函数 - 提供更多实用的对话分析功能
def get_last_user_message_content(conversation_id: str) -> Optional[str]:
    """
    获取对话中最后一条用户消息内容

    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[str]: 最后一条用户消息内容，若无用户消息则返回None
    """
    try:
        processor = ConversationMessageProcessor(conversation_id)
        return processor.get_last_message_by_role(MessageRole.USER)

    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 最后一条用户消息时发生异常: {str(e)}", exc_info=True)
        return None


def get_conversation_summary(conversation_id: str, max_messages_per_role: int = 3) -> Dict[str, Any]:
    """
    获取对话摘要信息

    Args:
        conversation_id (str): 对话ID
        max_messages_per_role (int): 每个角色最多显示的消息数量

    Returns:
        Dict[str, Any]: 对话摘要信息
    """
    try:
        processor = ConversationMessageProcessor(conversation_id)

        user_messages = processor.get_messages_by_role(MessageRole.USER)
        assistant_messages = processor.get_messages_by_role(MessageRole.ASSISTANT)

        # 限制消息数量
        recent_user_messages = user_messages[-max_messages_per_role:] if user_messages else []
        recent_assistant_messages = assistant_messages[-max_messages_per_role:] if assistant_messages else []

        return {
            "conversation_id": conversation_id,
            "total_user_messages": len(user_messages),
            "total_assistant_messages": len(assistant_messages),
            "recent_user_messages": recent_user_messages,
            "recent_assistant_messages": recent_assistant_messages,
            "last_user_message": processor.get_last_message_by_role(MessageRole.USER),
            "last_assistant_message": processor.get_last_message_by_role(MessageRole.ASSISTANT),
            "has_messages": len(user_messages) > 0 or len(assistant_messages) > 0
        }

    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 摘要时发生异常: {str(e)}", exc_info=True)
        return {
            "conversation_id": conversation_id,
            "error": str(e),
            "has_messages": False
        }


def format_conversation_for_notification(conversation_id: str, include_assistant: bool = True) -> Dict[str, Optional[str]]:
    """
    为通知消息格式化对话内容

    Args:
        conversation_id (str): 对话ID
        include_assistant (bool): 是否包含assistant回复

    Returns:
        Dict[str, Optional[str]]: 格式化后的对话内容
    """
    try:
        result = {
            "user_messages": get_all_user_messages_content(conversation_id),
            "last_assistant_message": None
        }

        if include_assistant:
            result["last_assistant_message"] = get_last_assistant_message_content(conversation_id)

        return result

    except Exception as e:
        logger.error(f"格式化对话 {conversation_id} 通知内容时发生异常: {str(e)}", exc_info=True)
        return {
            "user_messages": None,
            "last_assistant_message": None,
            "error": str(e)
        }
