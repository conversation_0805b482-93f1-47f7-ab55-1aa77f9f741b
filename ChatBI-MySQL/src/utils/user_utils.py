"""
User-related utility functions.
"""

from src.utils.logger import logger
from src.utils.in_memory_cache import in_memory_cache
import json
import urllib
import requests
import os

def get_valid_user_email(user_info: dict = {}) -> str:
    """
    Extract a valid email address from user information.
    
    Args:
        user_info: Dictionary containing user information
        
    Returns:
        str: A valid email address or "unknown" if none is found
    """
    email = user_info.get("email")
    if email and "@" in email:
        return email
    else:
        return user_info.get("enterprise_email", "unknown")
    

@in_memory_cache(expire_seconds=3000) # 应用缓存装饰器，缓存3000秒
def get_api_token(union_id: str):
    try:
        if union_id is None or union_id == '':
            logger.error("get_api_token当前登录无法获取到union_id")
            return ""
        
        api_endpoint = "/authentication/auth/authorized/feishuUnionIdLogin"
        # 构建完整的API URL（这里假设有一个基础URL，可以从环境变量获取或硬编码）
        base_url = os.getenv("SUMMERFARM_APP_API_PREFIX", "https://admin.summerfarm.net")
        if base_url == "":
            return "环境配置异常，SUMMERFARM_APP_API_PREFIX缺失"
        url = f"{base_url}{api_endpoint}"
    
        urlParam = json.dumps({"unionId": union_id})
        urlParam_dict = json.loads(urlParam) if urlParam else {}
        encoded_params = urllib.parse.urlencode(urlParam_dict)  # 对参数进行URL编码
          
        url_with_params = f"{url}?{encoded_params}"  # 拼接参数到URL
        logger.info(f"get_api_token转换后的url: {url_with_params}")
  
        response = requests.post(url_with_params)
        if response.status_code == 200:
            json_data = response.json()
            token = json_data.get("data", {}).get("token", "")
            logger.info(f"get_api_token被调用，调用结果: {response.status_code}, {json_data}, {token}")
            return token
        else:
            logger.error(f"get_api_token被调用，调用结果: {response.status_code}, {response.json()}")
    except Exception as e: 
        logger.exception("API get_api_token exception")
        return ""
