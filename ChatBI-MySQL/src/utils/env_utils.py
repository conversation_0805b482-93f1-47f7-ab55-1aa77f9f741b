"""
Environment utilities module.

This module provides utility functions for loading and accessing environment variables.
"""

import os
from typing import Any, Optional


def get_env(key: str, default: Any = None) -> Optional[str]:
    """
    Get an environment variable.
    
    Args:
        key: The name of the environment variable
        default: The default value to return if the environment variable is not set
        
    Returns:
        The value of the environment variable, or the default value if not set
    """
    return os.getenv(key, default)

def get_env_bool(key: str, default: bool = False) -> bool:
    """
    Get a boolean environment variable.
    
    Args:
        key: The name of the environment variable
        default: The default value to return if the environment variable is not set
        
    Returns:
        The boolean value of the environment variable, or the default value if not set
    """
    value = os.getenv(key)
    if value is None:
        return default
    return value.lower() in ('true', 'yes', '1', 'y')

def get_env_int(key: str, default: int = 0) -> int:
    """
    Get an integer environment variable.
    
    Args:
        key: The name of the environment variable
        default: The default value to return if the environment variable is not set
        
    Returns:
        The integer value of the environment variable, or the default value if not set
    """
    value = os.getenv(key)
    if value is None:
        return default
    try:
        return int(value)
    except ValueError:
        return default

def get_env_float(key: str, default: float = 0.0) -> float:
    """
    Get a float environment variable.
    
    Args:
        key: The name of the environment variable
        default: The default value to return if the environment variable is not set
        
    Returns:
        The float value of the environment variable, or the default value if not set
    """
    value = os.getenv(key)
    if value is None:
        return default
    try:
        return float(value)
    except ValueError:
        return default

def get_env_list(key: str, separator: str = ',', default: list = None) -> list:
    """
    Get a list environment variable by splitting a string.
    
    Args:
        key: The name of the environment variable
        separator: The separator to split the string by
        default: The default value to return if the environment variable is not set
        
    Returns:
        The list value of the environment variable, or the default value if not set
    """
    if default is None:
        default = []
    
    value = os.getenv(key)
    if value is None:
        return default
    
    return [item.strip() for item in value.split(separator) if item.strip()]

def get_admin_users() -> list:
    """
    Get the list of admin users from the environment.

    Returns:
        List of admin usernames (deduplicated)
    """
    admin_list = get_env_list('DASHBOARD_ADMINS', default=["唐鹏", "康凯", "王剑锋", "施正豪"])
    # Remove duplicates while preserving order
    seen = set()
    result = []
    for user in admin_list:
        if user not in seen:
            seen.add(user)
            result.append(user)
    return result
