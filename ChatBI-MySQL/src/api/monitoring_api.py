"""
监控API模块

提供系统监控相关的API端点。
"""

from flask import Blueprint, jsonify
from src.services.monitoring.db_pool_monitor import get_db_pool_status
from src.services.concurrency.user_query_limiter import user_query_limiter
from src.utils.logger import logger
import asyncio

monitoring_bp = Blueprint('monitoring', __name__)


@monitoring_bp.route('/api/monitoring/db-pools', methods=['GET'])
def get_database_pools_status():
    """获取数据库连接池状态"""
    try:
        status = get_db_pool_status()
        return jsonify({
            "success": True,
            "data": status
        })
    except Exception as e:
        logger.error(f"获取数据库连接池状态时出错: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@monitoring_bp.route('/api/monitoring/query-limits', methods=['GET'])
def get_query_limits_status():
    """获取查询限制器状态"""
    try:
        # 由于这是同步路由，我们需要在新的事件循环中运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            stats = loop.run_until_complete(user_query_limiter.get_global_stats())
        finally:
            loop.close()
        
        return jsonify({
            "success": True,
            "data": stats
        })
    except Exception as e:
        logger.error(f"获取查询限制器状态时出错: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@monitoring_bp.route('/api/monitoring/system-health', methods=['GET'])
def get_system_health():
    """获取系统整体健康状况"""
    try:
        # 获取数据库连接池状态
        db_status = get_db_pool_status()
        
        # 获取查询限制器状态
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            query_stats = loop.run_until_complete(user_query_limiter.get_global_stats())
        finally:
            loop.close()
        
        # 计算整体健康分数
        health_score = _calculate_health_score(db_status, query_stats)
        
        return jsonify({
            "success": True,
            "data": {
                "health_score": health_score,
                "status": "healthy" if health_score > 0.8 else "warning" if health_score > 0.5 else "critical",
                "database_pools": db_status,
                "query_limiter": query_stats,
                "recommendations": _get_health_recommendations(db_status, query_stats)
            }
        })
    except Exception as e:
        logger.error(f"获取系统健康状况时出错: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


def _calculate_health_score(db_status: dict, query_stats: dict) -> float:
    """计算系统健康分数（0-1之间）"""
    score = 1.0
    
    # 检查数据库连接池状态
    pools = db_status.get("pools", {})
    for pool_name, pool_info in pools.items():
        if pool_info.get("status") != "active":
            score -= 0.3  # 连接池不可用严重影响分数
        else:
            utilization = pool_info.get("utilization", 0)
            if utilization > 0.9:
                score -= 0.2  # 高使用率影响分数
            elif utilization > 0.8:
                score -= 0.1
    
    # 检查查询限制器状态
    active_queries = query_stats.get("total_active_queries", 0)
    max_possible = query_stats.get("total_users", 1) * query_stats.get("max_concurrent_per_user", 3)
    
    if max_possible > 0:
        query_load = active_queries / max_possible
        if query_load > 0.8:
            score -= 0.1
    
    return max(0.0, min(1.0, score))


def _get_health_recommendations(db_status: dict, query_stats: dict) -> list:
    """获取健康状况改进建议"""
    recommendations = []
    
    # 数据库连接池建议
    pools = db_status.get("pools", {})
    for pool_name, pool_info in pools.items():
        if pool_info.get("status") != "active":
            recommendations.append(f"连接池 {pool_name} 状态异常，请检查数据库连接")
        else:
            utilization = pool_info.get("utilization", 0)
            if utilization > 0.9:
                recommendations.append(f"连接池 {pool_name} 使用率过高({utilization:.1%})，建议增加连接池大小")
            elif utilization > 0.8:
                recommendations.append(f"连接池 {pool_name} 使用率较高({utilization:.1%})，请关注")
    
    # 查询限制器建议
    active_queries = query_stats.get("total_active_queries", 0)
    if active_queries > 20:
        recommendations.append(f"当前活跃查询数较多({active_queries})，可能存在慢查询")
    
    if not recommendations:
        recommendations.append("系统运行正常")
    
    return recommendations
