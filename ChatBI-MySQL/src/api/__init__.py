"""
API endpoints for the ChatBI-MySQL application.
"""
from flask import Flask

from .auth_api import auth_bp
from .dashboard_api import dashboard_bp
from .drive_api import drive_api
from .good_case_api import good_case_bp
from .history_api import history_bp
from .monitoring_api import monitoring_bp
# Import Blueprints
from .query_api import query_bp
from .recommendation_api import recommendation_bp
from .resource_api import resource_bp
from .share_api import share_bp


def register_routes(app: Flask):
    """
    Register all API routes with the Flask application.

    Args:
        app: The Flask application
    """
    # Register all blueprints
    app.register_blueprint(query_bp)
    app.register_blueprint(history_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(share_bp)
    app.register_blueprint(good_case_bp)
    app.register_blueprint(monitoring_bp)
    app.register_blueprint(recommendation_bp)
    app.register_blueprint(resource_bp)
    app.register_blueprint(drive_api)
