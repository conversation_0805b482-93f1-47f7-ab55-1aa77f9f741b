"""
用户Session清理服务
定期清理过期的用户session记录
"""

import threading

from src.utils.logger import logger
from src.services.auth.user_session_service import user_session_service


class SessionCleanupService:
    """Session清理服务类"""

    def __init__(self, cleanup_interval_hours=24):
        """
        初始化清理服务

        Args:
            cleanup_interval_hours: 清理间隔（小时），默认24小时
        """
        self.cleanup_interval_hours = cleanup_interval_hours
        self.cleanup_thread = None
        self.is_running = False
        self.stop_event = threading.Event()
    
    def start(self):
        """启动清理服务"""
        if self.is_running:
            logger.warning("Session清理服务已在运行中")
            return

        self.is_running = True
        self.stop_event.clear()  # 重置停止事件
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.info(f"Session清理服务已启动，清理间隔: {self.cleanup_interval_hours}小时")
    
    def stop(self):
        """停止清理服务"""
        self.is_running = False
        self.stop_event.set()  # 设置停止事件，立即唤醒等待中的线程

        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
            if self.cleanup_thread.is_alive():
                logger.warning("Session清理线程未能在超时时间内停止")
        logger.info("Session清理服务已停止")
    
    def _cleanup_loop(self):
        """清理循环"""
        while self.is_running:
            try:
                # 执行清理
                cleaned_count = user_session_service.cleanup_expired_sessions()
                logger.info(f"Session清理任务执行完成，清理了 {cleaned_count} 个过期session")

                # 使用Event.wait()替代循环睡眠，支持快速中断
                if self.stop_event.wait(timeout=self.cleanup_interval_hours * 3600):
                    # 如果stop_event被设置，立即退出循环
                    break

            except (SystemExit, KeyboardInterrupt):
                # 不捕获系统退出和键盘中断信号，让它们正常传播
                raise
            except Exception as e:
                logger.error(f"Session清理任务执行失败: {e}", exc_info=True)
                # 出错后等待1小时再重试，同样支持快速中断
                if self.stop_event.wait(timeout=3600):
                    break


# 全局清理服务实例
session_cleanup_service = SessionCleanupService()


def start_session_cleanup_service():
    """启动Session清理服务"""
    session_cleanup_service.start()


def stop_session_cleanup_service():
    """停止Session清理服务"""
    session_cleanup_service.stop()
