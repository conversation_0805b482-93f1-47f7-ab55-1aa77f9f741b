# Agent Module

This module provides agent functionality for the ChatBI-MySQL application. It is organized into a bot-centric architecture that allows for different types of bots with their own configurations.

## Structure

```
src/services/agent/
├── bots/                       # Different bot types
│   ├── base_bot.py             # Base class for all bots
│   ├── data_fetcher_bot.py     # Data fetcher bot implementation
│   └── qa_bot.py               # Q&A bot implementation (placeholder)
├── tools/                      # Shared tools used by bots
│   ├── data_tools.py           # Data-related tools (SQL, table samples)
│   ├── ddl_tools.py            # DDL-related tools
│   └── feishu_tools.py         # Feishu integration tools
├── utils/                      # Agent-specific utilities
│   ├── formatter.py            # Event formatting
│   ├── permissions.py          # Permission handling
│   └── model_provider.py       # LLM provider configuration
├── __init__.py                 # Main module exports
└── runner.py                   # Query execution and streaming
```

## Bot Architecture

Each bot type is a class that inherits from `BaseBot` and implements:

1. **LLM Selection**: Choose which model to use
2. **System Instructions**: Define the bot's behavior
3. **Permission Definitions**: Specify data access rules
4. **Tool Selection**: Choose which tools the bot can use

## Available Bots

### DataFetcherBot

The `DataFetcherBot` is specialized in:
- Fetching DDL information
- Writing and executing SQL queries
- Retrieving sample data from tables

### QABot (Placeholder)

The `QABot` is a placeholder for a future implementation that would:
- Answer general questions without querying a database
- Provide information about business processes, products, etc.

## Usage

To use a bot in your code:

```python
from src.services.agent import run_agent_query, DataFetcherBot

# Run a query with the default data fetcher bot
response_stream = run_agent_query(
    user_query="Show me sales data for last month",
    history=[],
    user_info=user_info,
    access_token=token,
    conversation_id=convo_id
)

# Or specify a bot type explicitly
response_stream = run_agent_query(
    user_query="Show me sales data for last month",
    history=[],
    user_info=user_info,
    access_token=token,
    conversation_id=convo_id,
    bot_type="data_fetcher"  # This is the default
)
```

## Adding a New Bot Type

To add a new bot type:

1. Create a new class in the `bots` directory that inherits from `BaseBot`
2. Implement all required methods
3. Add the bot to the `get_bot_class` function in `runner.py`
4. Update the `__init__.py` file to export the new bot

## Shared Tools

Tools are shared between bot types and organized by functionality:

- **data_tools.py**: Tools for querying data
- **ddl_tools.py**: Tools for fetching DDL information
- **feishu_tools.py**: Tools for integrating with Feishu

Each bot can choose which tools to use based on its needs.
