import aiohttp
import os
from dataclasses import dataclass
from typing import List
from src.utils.logger import logger

DEFAULT_WIKI_AUTH_API_KEY = "684a8a6639517167c39c4cef:w7CzJozBUxy1rhWcKu1FSyRwRB31p1fE"
DEFAULT_WIKI_DATASET_ID = "684aa95eaba1d13d05b4fc61"
AI_WIKI_DATASET_ID = "685236a016b3441e58f15ee0"

# 定义表示搜索结果中分数项的数据类
@dataclass
class ScoreItem:
    type: str
    value: float
    index: int

# 定义表示单个搜索结果项的数据类
@dataclass
class SearchResultItem:
    id: str
    updateTime: str
    q: str  # 文档原文片段
    a: str  # 文档答案片段 (如果存在)
    chunkIndex: int
    datasetId: str
    collectionId: str
    sourceId: str
    sourceName: str  # 文档来源名称
    score: List[ScoreItem]  # 分数列表
    tokens: int

import asyncio

async def fetch_search_results(dataset_id: str, text: str, limit: int, wiki_auth_api_key: str) -> List[SearchResultItem]:
    """
    异步发送搜索请求并返回结果。
    """
    url = "https://fastgpt-test.summerfarm.net/api/core/dataset/searchTest"
    headers = {
        'content-type': 'application/json'
    }

    # 使用 cookie 认证方式，而不是 Bearer token
    cookies = {
        'NEXT_LOCALE': 'en',
        'fastgpt_token': wiki_auth_api_key,
        'NEXT_DEVICE_SIZE': 'mobile'
    }
    payload = {
        "datasetId": dataset_id,
        "text": text,
        "searchMode": "mixedRecall",  # 使用混合召回模式(默认) 或者 "embeddingRecall"
        "embeddingWeight": 0.5,  # 嵌入权重
        "usingReRank": True,  # 使用重排序
        "rerankWeight": 0.5,  # 重排序权重
        "limit": limit,
        "similarity": 0,  # 相似度阈值设为0
        "datasetSearchUsingExtensionQuery": False,  # 不使用扩展查询
        "datasetSearchExtensionModel": "",  # 扩展模型为空
        "datasetSearchExtensionBg": ""  # 扩展背景为空
    }

    try:
        logger.info(f"开始搜索: dataset_id={dataset_id}, text={text}")
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, cookies=cookies, json=payload) as response:
                response_text = await response.text()
                logger.info(f"搜索响应: {response_text[:100]}...")
                response.raise_for_status()
                response_data = await response.json()

                if response_data.get('code') != 200:
                    logger.error(f"API 返回错误: {response_data.get('message')}")
                    return []

                # 处理搜索结果
                search_data_list = []
                raw_results = response_data.get('data', {}).get('list', [])
                if raw_results:
                    for item_dict in raw_results:
                        item_dict.setdefault('tokens', 0)
                        score_data = item_dict.get('score', [])
                        processed_scores = [
                            ScoreItem(**s_item) for s_item in score_data if isinstance(s_item, dict)
                        ]
                        try:
                            result_item = SearchResultItem(
                                id=item_dict.get('id'),
                                updateTime=item_dict.get('updateTime'),
                                q=item_dict.get('q'),
                                a=item_dict.get('a'),
                                chunkIndex=item_dict.get('chunkIndex'),
                                datasetId=item_dict.get('datasetId'),
                                collectionId=item_dict.get('collectionId'),
                                sourceId=item_dict.get('sourceId'),
                                sourceName=item_dict.get('sourceName'),
                                score=processed_scores,
                                tokens=item_dict.get('tokens')
                            )
                            search_data_list.append(result_item)
                        except TypeError as te:
                            logger.error(f"创建 SearchResultItem 失败: {te}, 数据: {item_dict}")
                return search_data_list
    except Exception as e:
        logger.exception(f"搜索请求失败: {e}")
        return []

async def search_docs_by_text(
    text: str,
    limit: int = 3000,
) -> List[SearchResultItem]:
    """
    通过文本搜索文档知识库。

    功能描述:
    - 该工具提供一个结构化查询接口，用于从知识库中获取与“乳制品”“奶油”“奶油芝士”“植物奶”等烘焙和餐饮原材料相关的专业知识。
    - 知识内容包括但不限于产品产地、成分类型、储存方式、保质期、使用建议、口感描述、适用场景和常见问题。

    使用说明:
    - 输入参数 `text` 是用户提供的查询文本，工具会根据该文本搜索相关知识。
    - 可选参数 `limit` 用于限制返回结果的数量，默认为3000条。
    - 【重要】此工具的查询操作非常消耗资源，用户的单个问题最多不可超过2次请求。

    输出要求:
    - 返回一个包含搜索结果的列表，每个结果是一个 `SearchResultItem` 对象，包含相关的文档信息。
    - 【重要】在使用该工具后，AI需要在回复中明确告知用户哪些内容引用了该工具的查询结果。
    - 【重要】生成最终回复时，需要在内容的最后包含数据来源信息。例如：参考文献：【乳制品 李寻之 2.0（1）.pdf】。以此类推。

    注意事项:
    - AI在生成回复时，需确保引用内容的准确性，并避免遗漏数据来源标注。
    - 如果查询结果为空或发生错误，需向用户说明原因，并建议调整查询文本或稍后重试。

    Args:
        text (str): 用户提供的查询文本。
        limit (int, optional): 限制返回结果的字符数量，默认为3000。

    Returns:
        List[SearchResultItem]: 包含搜索结果的列表，每个结果是一个结构化的 `SearchResultItem` 对象。
    """
    dataset_ids = [
        os.getenv("WIKI_DATASET_ID", DEFAULT_WIKI_DATASET_ID),
        os.getenv("AI_WIKI_DATASET_ID", AI_WIKI_DATASET_ID)
    ]
    wiki_auth_api_key = os.getenv("WIKI_AUTH_API_KEY", DEFAULT_WIKI_AUTH_API_KEY)

    if not all(dataset_ids) or not wiki_auth_api_key:
        logger.error("错误: 未找到必要的环境变量 WIKI_DATASET_ID 或 WIKI_AUTH_API_KEY")
        return []

    # 并发搜索两个数据集
    tasks = [
        fetch_search_results(dataset_id, text, limit, wiki_auth_api_key)
        for dataset_id in dataset_ids
    ]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 合并结果并过滤异常
    merged_results = []
    for result in results:
        if isinstance(result, list):
            merged_results.extend(result)
        else:
            logger.error(f"搜索任务失败: {result}")

    return merged_results
    
from src.services.agent.tools.tool_manager import tool_manager
tool_manager.register_as_function_tool(search_docs_by_text)
