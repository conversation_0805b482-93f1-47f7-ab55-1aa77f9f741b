"""
工具管理器模块，用于管理和加载代理工具。

该模块提供了一个工具管理器类，可以根据配置文件加载指定的工具，
并提供工具的注册和获取功能。
"""

from typing import Dict, List, Any, Callable, Optional, Set

from agents import function_tool,FunctionTool
from src.utils.logger import logger


class ToolManager:
    """
    工具管理器类，用于管理和加载代理工具。

    该类提供了以下功能：
    1. 注册工具：将工具函数注册到工具管理器中
    2. 获取工具：根据工具名称获取工具函数
    3. 加载工具：根据配置文件加载指定的工具
    4. 获取所有工具：获取所有已注册的工具
    5. 获取工具列表：根据工具名称列表获取工具函数列表
    """

    _instance = None

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(ToolManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化工具管理器"""
        if self._initialized:
            return

        # 工具字典，键为工具名称，值为工具函数
        self._tools: Dict[str, FunctionTool] = {}

        # 初始化完成标志
        self._initialized = True

    def register_as_function_tool(self, tool_func: Callable) -> FunctionTool:
        """
        手动将函数注册为工具函数。

        这个方法允许程序员手动选择是否将函数注册为工具。
        与使用@function_tool装饰器不同，这个方法不会修改原始函数，
        只是将其注册到工具管理器中。

        Args:
            tool_func: 要注册为工具的函数

        Returns:
            原始函数，不做任何修改

        示例:
            ```python
            @tool_manager.register_as_function_tool
            def my_tool():
                # 工具实现
                pass
            ```
        """
        # 保存原始函数名称
        original_name = tool_func.__name__

        # 设置标记属性
        setattr(tool_func, "__register_as_tool__", True)

        # 如果函数没有被function_tool装饰，则在这里装饰
        if not hasattr(tool_func, "__function_tool__"):
            tool_func = function_tool(tool_func)

        # 注册工具，使用原始函数名称作为键
        self._tools[original_name] = tool_func
        logger.info(f"已注册工具: {original_name}")
        return tool_func

    def get_tool(self, tool_name: str) -> Optional[FunctionTool]:
        """
        根据工具名称获取工具函数。

        Args:
            tool_name: 工具名称

        Returns:
            工具函数，如果工具不存在则返回None
        """
        return self._tools.get(tool_name)

    def get_all_tools(self) -> Dict[str, FunctionTool]:
        """
        获取所有已注册的工具。

        Returns:
            工具字典，键为工具名称，值为工具函数
        """
        return self._tools.copy()

    def get_tool_list(self, tool_names: List[str]) -> List[FunctionTool]:
        """
        根据工具名称列表获取工具函数列表。

        Args:
            tool_names: 工具名称列表

        Returns:
            工具函数列表，如果工具不存在则跳过
        """
        tools = []
        for name in tool_names:
            tool = self.get_tool(name)
            if tool:
                tools.append(tool)
            else:
                logger.warning(f"工具 {name} 不存在，已跳过")
        return tools


# 创建工具管理器实例
tool_manager = ToolManager()
