from src.utils.resource_manager import load_resource
from src.utils.resource_manager import list_resources
import os
# 导入全局日志处理类
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager

async def read_api_llms(question: str) -> str:
    """
    根据客户的问题，获取对应的API文档接口内容。
    Args:
        question: 客户的问题

    Returns:
        拼接后的所有文件内容字符串，每个文件内容之间用换行符分隔；如果读取失败则返回None
    """
    logger.info("开始读取api_llms目录下的所有文件的内容")
    try:
        data_fetcher_config = list_resources('api_llms', '.md')
        contentList = []
        for config in data_fetcher_config:
            api_llms_content = load_resource('api_llms', config)
            contentList.append(api_llms_content)

        logger.info("读取api_llms目录下的所有文件的内容成功")
        # logger.info("读取api_llms目录下的所有文件的内容成功: %s", contentList)
        return "\n".join(contentList)
    except Exception as e:
        logger.exception(f"读取api_llms文件内容时出错")
        return "读取文件内容时出错"

tool_manager.register_as_function_tool(read_api_llms)