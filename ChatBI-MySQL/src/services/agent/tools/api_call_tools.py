import os
import aiohttp
import json
import urllib.parse

from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.services.agent.tools.tool_manager import tool_manager
# 导入全局日志处理类
from src.utils.logger import logger

async def api_call_tool(wrapper: RunContextWrapper[UserInfo],
                        query: str,
                        api_endpoint: str,
                        headerList: str,
                        method: str,
                        urlParam: str,
                        params: str) -> str:
    """当需要api调用时，使用此工具进行调用

    Args:
        wrapper: 调用的上下文
        query: 查询问题
        api_endpoint: API端点路径，例如 "/api/v1/materials"
        headerList: 请求头信息的JSON字符串，包含Content-Type等头部信息
        method: HTTP方法，默认为"GET"，可选值包括"POST"、"PUT"、"DELETE"等
        urlParam: 拼接到url的参数
        params: API调用参数的JSON字符串，对于GET请求是查询参数，对于POST/PUT请求是请求体

    Returns:
        str: API调用结果
    """

    logger.info(f"API调用工具被调用，参数如下:")
    logger.info(f"api_endpoint: {api_endpoint}")
    logger.info(f"查询: {query}")
    logger.info(f"HTTP方法: {method}")
    logger.info(f"参数字符串: {params}")

    if wrapper is None:
        logger.error("用户上下文不存在")
        return ""
    user_info=wrapper.context
    logger.info(f"用户上下文:{user_info}")

    # 将JSON字符串转换为Python字典
    try:
        headerList_dict = json.loads(headerList) if headerList else {}
        params_dict = json.loads(params) if params else {}
        urlParam_dict = json.loads(urlParam) if urlParam else {}
        logger.info(f"转换后的headers: {json.dumps(headerList_dict, indent=2)}")
        logger.info(f"转换后的urlParam: {json.dumps(urlParam_dict, indent=2)}")
        logger.info(f"转换后的参数: {json.dumps(params_dict, indent=2)}")
    except json.JSONDecodeError as e:
        return f"JSON解析错误: {str(e)}，请确保提供有效的JSON字符串"

    # 如果没有提供API端点，则返回错误信息
    if not api_endpoint:
        return f"无法执行API调用：缺少API端点。请提供有效的API端点路径。"

    # 构建完整的API URL（这里假设有一个基础URL，可以从环境变量获取或硬编码）
    base_url = os.getenv("SUMMERFARM_APP_API_PREFIX", "")
    if base_url == "":
        return "环境配置异常，SUMMERFARM_APP_API_PREFIX缺失"
    url = f"{base_url}{api_endpoint}"
    logger.info(f"转换后的url: {url}")

    if not headerList_dict:
        headerList_dict = {"Content-Type": "application/json"}
    # headerList追加content-type
    if ("Content-Type" not in headerList_dict):
        headerList_dict["Content-Type"] = "application/json"

    # 处理token
    token = user_info.summerfarm_api_token
    headerList_dict["token"] = f"{token}"
    logger.info(f"headers字符串: {headerList_dict}")

    try:
        async with aiohttp.ClientSession() as session:
            # 根据HTTP方法执行不同的请求
            if method.upper() == "GET":
                url_with_params = url
                # 拼接参数到URL
                if urlParam_dict:
                    encoded_params = urllib.parse.urlencode(urlParam_dict)  # 对参数进行URL编码
                    url_with_params = f"{url}?{encoded_params}"  # 拼接参数到URL
                async with session.get(url_with_params, headers=headerList_dict, params=params_dict) as response:
                    result = await response.text()
                    logger.info(f"API调用结果: {response.status}, {result}")
                    return result
            elif method.upper() == "POST":
                url_with_params = url
                # 拼接参数到URL
                if urlParam_dict:
                    encoded_params = urllib.parse.urlencode(urlParam_dict)  # 对参数进行URL编码
                    url_with_params = f"{url}?{encoded_params}"  # 拼接参数到URL
                async with session.post(url_with_params, headers=headerList_dict, json=params_dict) as response:
                    result = await response.text()
                    logger.info(f"API调用结果: {response.status}, {result}")
                    return result
            elif method.upper() == "PUT":
                async with session.put(url, headers=headerList_dict, json=params_dict) as response:
                    result = await response.text()
                    logger.info(f"API调用结果: {response.status}, {result}")
                    return result
            elif method.upper() == "DELETE":
                async with session.delete(url, headers=headerList_dict, params=params_dict) as response:
                    result = await response.text()
                    logger.info(f"API调用结果: {response.status}, {result}")
                    return result
            else:
                return f"不支持的HTTP方法: {method}"

    except Exception as e:
        return f"API调用过程中发生错误: {str(e)}"

tool_manager.register_as_function_tool(api_call_tool)