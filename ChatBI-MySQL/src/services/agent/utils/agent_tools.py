"""
Agent工具化封装模块

将专业Agent封装为可被主Agent调用的工具函数，实现Agent as Tool架构。
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime

from agents import function_tool, Agent, Runner, RunConfig, ModelSettings
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.utils.logger import logger
from src.utils.resource_manager import list_resources


@dataclass
class AgentExecutionLog:
    """Agent执行过程的详细日志"""
    timestamp: str
    event_type: str  # 事件类型：tool_call, tool_output, log, error等
    content: str     # 日志内容
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外的元数据


@dataclass
class AgentToolResult:
    """Agent工具执行结果 - 保持简洁，不包含详细日志"""
    success: bool
    data: Any = None
    sql: Optional[str] = None
    error: Optional[str] = None
    agent_name: Optional[str] = None
    execution_time: Optional[float] = None
    # 只保留统计信息，不保存详细日志内容
    tool_calls_count: int = 0
    tools_used_count: int = 0


class AgentToolExecutor:
    """Agent工具执行器，提供统一的执行和错误处理"""

    def __init__(self, max_retries: int = 2, timeout: int = 600):
        self.max_retries = max_retries
        self.timeout = timeout
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "timeout_executions": 0,
            "avg_execution_time": 0.0
        }
        # 用于追踪当前查询中使用的所有agent
        self.used_agents = []
    
    async def execute_agent_query(
        self,
        agent: Agent,
        query: str,
        user_context: Any,
        agent_name: str = "unknown"
    ) -> AgentToolResult:
        """
        执行Agent查询，带重试和超时控制，并捕获详细的执行日志

        Args:
            agent: 要执行的Agent实例
            query: 用户查询
            user_context: 用户上下文
            agent_name: Agent名称，用于日志

        Returns:
            AgentToolResult: 执行结果，包含详细的执行日志
        """
        start_time = time.time()

        # 更新统计信息
        self.execution_stats["total_executions"] += 1

        for attempt in range(1, self.max_retries + 1):
            try:
                logger.info(f"🔧 执行{agent_name}工具，第{attempt}次尝试")

                # 使用流式执行来捕获详细日志
                result, execution_logs = await asyncio.wait_for(
                    self._execute_with_stream_logging(
                        agent, query, user_context, agent_name
                    ),
                    timeout=self.timeout
                )

                execution_time = time.time() - start_time
                # 更新结果中的执行时间
                result.execution_time = execution_time

                logger.info(f"✅ {agent_name}工具执行成功，耗时{execution_time:.2f}秒")

                # 记录使用的agent
                if agent_name not in self.used_agents:
                    self.used_agents.append(agent_name)

                # 更新成功统计
                self.execution_stats["successful_executions"] += 1
                self._update_avg_execution_time(execution_time)

                # 返回结果和日志的元组
                return result, execution_logs
                
            except asyncio.TimeoutError:
                error_msg = f"{agent_name}工具执行超时（{self.timeout}秒）"
                logger.warning(f"⏰ {error_msg}，第{attempt}次尝试")
                if attempt == self.max_retries:
                    self.execution_stats["timeout_executions"] += 1
                    self.execution_stats["failed_executions"] += 1
                    return AgentToolResult(
                        success=False,
                        error=error_msg,
                        agent_name=agent_name,
                        execution_time=time.time() - start_time
                    )
                    
            except Exception as e:
                error_msg = f"{agent_name}工具执行出错: {str(e)}"
                logger.exception(f"❌ {error_msg}，第{attempt}次尝试")
                if attempt == self.max_retries:
                    self.execution_stats["failed_executions"] += 1
                    return AgentToolResult(
                        success=False,
                        error=error_msg,
                        agent_name=agent_name,
                        execution_time=time.time() - start_time
                    )
                    
                # 短暂等待后重试
                await asyncio.sleep(1)

    def get_used_agents(self) -> List[str]:
        """获取当前查询中使用的所有agent列表"""
        return self.used_agents.copy()

    def reset_used_agents(self):
        """重置使用的agent列表，用于新的查询"""
        self.used_agents = []

    def get_used_agents_string(self) -> str:
        """获取使用的agent列表的字符串表示，用于存储到数据库"""
        if not self.used_agents:
            return "coordinator_bot"

        # 确保coordinator_bot在列表开头
        agents = self.used_agents.copy()
        if "coordinator_bot" not in agents:
            agents.insert(0, "coordinator_bot")
        elif agents[0] != "coordinator_bot":
            agents.remove("coordinator_bot")
            agents.insert(0, "coordinator_bot")

        return ",".join(agents)
    
    def _update_avg_execution_time(self, execution_time: float):
        """更新平均执行时间"""
        current_avg = self.execution_stats["avg_execution_time"]
        successful_count = self.execution_stats["successful_executions"]

        if successful_count == 1:
            self.execution_stats["avg_execution_time"] = execution_time
        else:
            # 计算新的平均值
            self.execution_stats["avg_execution_time"] = (
                (current_avg * (successful_count - 1) + execution_time) / successful_count
            )

    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return self.execution_stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "timeout_executions": 0,
            "avg_execution_time": 0.0
        }

    async def _execute_with_stream_logging(
        self,
        agent: Agent,
        query: str,
        user_context: Any,
        agent_name: str
    ) -> tuple[AgentToolResult, List[AgentExecutionLog]]:
        """
        使用流式执行并捕获详细的执行日志

        Args:
            agent: Agent实例
            query: 查询内容
            user_context: 用户上下文
            agent_name: Agent名称

        Returns:
            tuple: (简洁的执行结果, 详细的执行日志列表)
        """
        from agents import Runner, RunConfig, ModelSettings
        from src.services.agent.utils.formatter import format_event_message

        execution_logs = []
        tools_used_set = set()
        tool_calls_count = 0

        try:
            # 使用流式执行
            result = Runner.run_streamed(
                agent,
                input=query,
                max_turns=10,
                run_config=RunConfig(
                    model_settings=ModelSettings(temperature=0.1)
                ),
                context=user_context,
            )

            # 处理流事件并收集日志
            async for event in result.stream_events():
                # 格式化事件消息
                formatted_message = format_event_message(event)
                if formatted_message:
                    msg_type = formatted_message.get("type", "unknown")
                    content = formatted_message.get("content", "")

                    # 创建执行日志条目
                    log_entry = AgentExecutionLog(
                        timestamp=datetime.now().isoformat(),
                        event_type=msg_type,
                        content=content,
                        metadata={
                            "agent_name": agent_name,
                            "event_raw_type": event.type
                        }
                    )
                    execution_logs.append(log_entry)

                    # 统计工具调用
                    if msg_type == "tool_call":
                        tool_calls_count += 1
                        # 尝试提取工具名称
                        if "工具:" in content:
                            tool_name = content.split("工具:")[1].split()[0] if "工具:" in content else "unknown"
                            tools_used_set.add(tool_name)

                    # 记录重要的日志信息
                    if msg_type in ["tool_call", "tool_output", "error"]:
                        logger.info(f"[{agent_name}] {msg_type}: {content[:100]}...")

            # 获取最终结果
            final_output = result.final_output if hasattr(result, 'final_output') else str(result)
            sql = self._extract_sql_from_result(result)

            # 返回简洁的结果和单独的日志
            simple_result = AgentToolResult(
                success=True,
                data=final_output,
                sql=sql,
                agent_name=agent_name,
                execution_time=0,  # 将在上层计算
                tool_calls_count=tool_calls_count,
                tools_used_count=len(tools_used_set)
            )

            return simple_result, execution_logs

        except Exception as e:
            # 记录错误日志
            error_log = AgentExecutionLog(
                timestamp=datetime.now().isoformat(),
                event_type="error",
                content=f"执行出错: {str(e)}",
                metadata={"agent_name": agent_name}
            )
            execution_logs.append(error_log)

            # 返回简洁的错误结果和日志
            error_result = AgentToolResult(
                success=False,
                error=str(e),
                agent_name=agent_name,
                execution_time=0,
                tool_calls_count=tool_calls_count,
                tools_used_count=len(tools_used_set)
            )

            return error_result, execution_logs

    def _extract_sql_from_result(self, result) -> Optional[str]:
        """从Agent执行结果中提取SQL语句"""
        try:
            # 这里可以根据实际的结果格式来提取SQL
            # 暂时返回None，后续可以根据需要实现
            return None
        except Exception:
            return None


class AgentToolRegistry:
    """Agent工具注册中心"""
    
    def __init__(self, user_info: Dict[str, Any]):
        self.user_info = user_info
        self.executor = AgentToolExecutor()
        self.registered_tools = {}
        # 保存简洁的执行结果（不包含详细日志）
        self.execution_results: List[AgentToolResult] = []
        # 单独管理详细的执行日志
        self.execution_logs: Dict[str, List[AgentExecutionLog]] = {}
        # 执行统计信息
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "agents_used": []
        }

    def get_used_agents_string(self) -> str:
        """获取本次查询使用的所有agent的字符串表示"""
        return self.executor.get_used_agents_string()

    def reset_agent_tracking(self):
        """重置agent追踪，用于新的查询"""
        self.executor.reset_used_agents()
        # 清空之前的执行结果和日志
        self.execution_results.clear()
        self.execution_logs.clear()
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "agents_used": []
        }

    def add_execution_result(self, result: AgentToolResult):
        """添加简洁的执行结果到记录中"""
        self.execution_results.append(result)
        # 更新统计信息
        self.execution_stats["total_executions"] += 1
        if result.success:
            self.execution_stats["successful_executions"] += 1
        else:
            self.execution_stats["failed_executions"] += 1

        if result.agent_name and result.agent_name not in self.execution_stats["agents_used"]:
            self.execution_stats["agents_used"].append(result.agent_name)

    def add_execution_logs(self, agent_name: str, logs: List[AgentExecutionLog]):
        """单独添加执行日志"""
        if agent_name not in self.execution_logs:
            self.execution_logs[agent_name] = []
        self.execution_logs[agent_name].extend(logs)

    def get_all_execution_logs(self) -> str:
        """
        获取所有Tool Agent的聚合执行日志

        Returns:
            str: 格式化的聚合日志字符串
        """
        if not self.execution_results and not self.execution_logs:
            return "无Tool Agent执行记录"

        log_lines = []
        log_lines.append("=== Tool Agent 详细执行日志 ===")

        # 遍历执行结果
        for i, result in enumerate(self.execution_results, 1):
            log_lines.append(f"\n--- Agent {i}: {result.agent_name} ---")
            log_lines.append(f"执行状态: {'成功' if result.success else '失败'}")
            log_lines.append(f"执行时间: {result.execution_time:.2f}秒")
            log_lines.append(f"工具调用次数: {result.tool_calls_count}")
            log_lines.append(f"使用的工具数量: {result.tools_used_count}")

            if result.error:
                log_lines.append(f"错误信息: {result.error}")

            # 从单独的日志存储中获取详细日志
            agent_logs = self.execution_logs.get(result.agent_name, [])
            if agent_logs:
                log_lines.append("执行过程:")
                single_line_data = None
                for log_entry in agent_logs:
                    timestamp = log_entry.timestamp
                    timestamp = timestamp.split('.')[0].split('T')[1] if 'T' in timestamp else timestamp.split('.')[0]
                    content = log_entry.content
                    if len(content) > 5000:
                        content = content[:5000] + '...'
                    if "data" == log_entry.event_type:
                        # 如果是data类型，则不添加时间戳, 且合并为一行
                        single_line_data = f"{single_line_data}{content}"
                    else:
                        if single_line_data:
                            log_lines.append(single_line_data)
                            single_line_data = None
                        log_lines.append(f"[{timestamp}] {log_entry.event_type}: {content}")
                if single_line_data:
                    log_lines.append(single_line_data)
            else:
                log_lines.append("执行过程: 无详细日志记录")

        log_lines.append("=== Tool Agent 日志结束 ===")
        return "\n".join(log_lines)

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要统计"""
        total_time = sum(r.execution_time for r in self.execution_results if r.execution_time)
        total_logs = sum(len(logs) for logs in self.execution_logs.values())

        return {
            "total": self.execution_stats["total_executions"],
            "successful": self.execution_stats["successful_executions"],
            "failed": self.execution_stats["failed_executions"],
            "agents": self.execution_stats["agents_used"].copy(),
            "total_execution_time": total_time,
            "avg_execution_time": total_time / len(self.execution_results) if self.execution_results else 0,
            "total_log_entries": total_logs
        }
    
    def get_all_tools(self) -> List[Callable]:
        """获取所有注册的工具"""
        return list(self.registered_tools.values())
    
    def get_tool_by_name(self, name: str) -> Optional[Callable]:
        """根据名称获取工具"""
        return self.registered_tools.get(name)

    async def _execute_agent_directly(self, agent_name: str, query: str) -> Optional[str]:
        """
        直接执行Agent，用于并行分析工具

        Args:
            agent_name: Agent名称
            query: 查询内容

        Returns:
            str: 执行结果的JSON字符串，如果失败返回None
        """
        try:
            # 查找对应的Bot配置
            data_fetcher_configs = list_resources('data_fetcher_bot_config', '.yml')

            for config_file in data_fetcher_configs:
                try:
                    bot = DataFetcherBot(self.user_info, config_file)
                    if bot.config.get('agent_name') == agent_name:
                        # 找到对应的Bot，执行查询
                        agent = bot.create_agent()

                        # 创建用户上下文对象
                        from src.services.feishu.user_service import UserService
                        user_context = UserService.create_user_info_object(self.user_info)

                        # 执行查询，获取简洁结果和详细日志
                        result, execution_logs = await self.executor.execute_agent_query(
                            agent=agent,
                            query=query,
                            user_context=user_context,
                            agent_name=agent_name
                        )

                        # 分别保存简洁结果和详细日志到注册中心
                        self.add_execution_result(result)
                        self.add_execution_logs(agent_name, execution_logs)

                        # 返回JSON格式的结果
                        response_data = {
                            "success": result.success,
                            "agent_name": result.agent_name,
                            "data": result.data,
                            "sql": result.sql,
                            "error": result.error,
                            "execution_time": result.execution_time
                        }

                        return json.dumps(response_data, ensure_ascii=False, indent=2)

                except Exception as e:
                    logger.exception(f"执行Agent {agent_name} 时出错: {e}")
                    continue

            # 如果没有找到对应的Agent
            logger.warning(f"未找到Agent: {agent_name}")
            return json.dumps({
                "success": False,
                "error": f"未找到Agent: {agent_name}",
                "agent_name": agent_name
            }, ensure_ascii=False)

        except Exception as e:
            logger.exception(f"直接执行Agent {agent_name} 时出错: {e}")
            return json.dumps({
                "success": False,
                "error": f"执行Agent时出错: {str(e)}",
                "agent_name": agent_name
            }, ensure_ascii=False)


# 并行执行工具
def create_parallel_analysis_tool(registry: AgentToolRegistry) -> Callable:
    """创建并行分析工具"""

    @function_tool(
        name_override="run_parallel_analysis",
        description_override="并行运行多个专业分析，适用于需要多个领域协作的复杂查询"
    )
    async def parallel_analysis_tool(analysis_requests: str) -> str:
        """
        并行执行多个专业分析

        Args:
            analysis_requests: JSON格式的分析请求列表，格式：
                [{"agent_name": "sales_order_analytics", "query": "查询销售数据"}]

        Returns:
            str: 所有分析结果的JSON字符串
        """
        try:
            # 解析请求
            requests = json.loads(analysis_requests)
            if not isinstance(requests, list):
                return json.dumps({
                    "success": False,
                    "error": "analysis_requests必须是JSON数组格式"
                }, ensure_ascii=False)

            # 准备并行任务
            tasks = []
            for req in requests:
                agent_name = req.get('agent_name')
                query = req.get('query')

                if not agent_name or not query:
                    continue

                # 直接使用registry的执行器来执行Agent
                # 而不是尝试调用FunctionTool对象
                task = registry._execute_agent_directly(agent_name, query)
                if task:
                    tasks.append(task)

            if not tasks:
                return json.dumps({
                    "success": False,
                    "error": "没有找到有效的分析请求"
                }, ensure_ascii=False)

            # 并行执行
            logger.info(f"🚀 开始并行执行{len(tasks)}个分析任务")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        "success": False,
                        "error": str(result),
                        "request_index": i
                    })
                else:
                    # 尝试解析JSON结果
                    try:
                        parsed_result = json.loads(result) if isinstance(result, str) else result
                        parsed_result["request_index"] = i
                        processed_results.append(parsed_result)
                    except:
                        processed_results.append({
                            "success": False,
                            "error": "结果解析失败",
                            "raw_result": str(result),
                            "request_index": i
                        })

            return json.dumps({
                "success": True,
                "results": processed_results,
                "total_tasks": len(tasks)
            }, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.exception(f"并行分析工具执行异常: {e}")
            return json.dumps({
                "success": False,
                "error": f"并行分析执行异常: {str(e)}"
            }, ensure_ascii=False)

    return parallel_analysis_tool
