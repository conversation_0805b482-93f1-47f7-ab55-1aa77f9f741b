"""
Database metadata service module.

This module provides business logic for retrieving database metadata.
"""

from typing import Dict, Any

from src.utils.logger import logger
from src.repositories.xianmudb.metadata import (
    get_table_sample_data as get_sample_data,
    get_database_schema as get_schema,
)
from src.models.query_result import SQLQueryResult

def get_table_sample_data(table_name: str, limit: int = 5) -> SQLQueryResult:
    """
    Get sample data from a table.

    Args:
        table_name (str): The name of the table
        limit (int, optional): The maximum number of rows to return. Defaults to 5.

    Returns:
        SQLQueryResult: The sample data
    """
    logger.info(f"Getting sample data for table {table_name} (limit: {limit})")
    result = get_sample_data(table_name, limit)
    return result

def get_database_schema() -> Dict[str, Any]:
    """
    Get the xianmudb of the database.

    Returns:
        Dict[str, Any]: The database xianmudb
    """
    logger.info("Getting database xianmudb")
    return get_schema()
