"""
飞书消息解析模块
负责解析和验证飞书消息内容
"""
import json
from src.utils.logger import logger
from .config import FeishuConfig


class MessageParser:
    """飞书消息解析器"""

    @staticmethod
    def check_content_has_at_all(content_data: dict) -> bool:
        """检查消息内容中是否包含 @_all

        Args:
            content_data: 解析后的消息内容字典

        Returns:
            bool: 如果消息中包含 @_all 则返回 True，否则返回 False
        """
        # 处理纯文本消息
        if "text" in content_data:
            return "@_all" in content_data.get("text", "")

        # 处理富文本消息
        if "content" in content_data:
            content_array = content_data.get("content", [])
            for content_block in content_array:
                for element in content_block:
                    if element.get("tag") == "at" and element.get("user_id") == "@_all":
                        return True
        return False

    @staticmethod
    def extract_text_from_content(content_data: dict) -> str:
        """从消息内容中提取文本

        Args:
            content_data: 解析后的消息内容字典

        Returns:
            str: 提取出的文本内容
        """
        # 处理纯文本消息
        if "text" in content_data:
            return content_data.get("text", "")

        # 处理富文本消息
        text_content = ""
        if "content" in content_data:
            content_array = content_data.get("content", [])
            for content_block in content_array:
                for element in content_block:
                    if element.get("tag") == "text":
                        text_content += element.get("text", "")
        return text_content

    @staticmethod
    def extract_image_key_from_content(content_data: dict) -> str | None:
        """从消息内容中提取图片key

        Args:
            content_data: 解析后的消息内容字典

        Returns:
            str | None: 提取出的图片key，如果没有则返回None
        """
        # 处理富文本消息
        if "content" in content_data:
            content_array = content_data.get("content", [])
            for content_block in content_array:
                for element in content_block:
                    if element.get("tag") == "img":
                        return element.get("image_key")
        return None

    @staticmethod
    def clean_user_query(user_query: str) -> str:
        """清理用户查询内容

        Args:
            user_query: 原始用户查询

        Returns:
            str: 清理后的用户查询
        """
        # 移除@_user_1提及并清理空白字符
        if "@_user_1" in user_query:
            user_query = user_query.replace("@_user_1", "").strip()

        # 移除开头的冒号（中文和英文）
        user_query = user_query.strip()
        if user_query.startswith(":"):
            user_query = user_query[1:].strip()
        elif user_query.startswith("："):
            user_query = user_query[1:].strip()

        return user_query.strip()

    @staticmethod
    def validate_query_length(user_query: str, is_new_conversation: bool = False) -> bool:
        """验证查询长度是否合法

        Args:
            user_query: 用户查询内容
            is_new_conversation: 是否为新对话

        Returns:
            bool: 查询长度是否合法
        """
        if is_new_conversation and len(user_query.strip()) <= FeishuConfig.get_min_query_length():
            return False
        return True

    @staticmethod
    def get_query_too_short_reply() -> str:
        """获取查询过短时的回复内容

        Returns:
            str: 回复内容
        """
        return """您的问题太短了，您可尝试这样提问：
1. 我的客户中，过去30天购买了安佳淡奶油的有哪些？列出他们的详细信息。
2. 杭州市过去7天新增了多少新注册的门店？
3. 我的客户中，有哪些客户在过去6个月曾经购买过10单以上，但是最近30天都未再来下单的？列出他们的详细信息（流失用户分析）。
4. 杭州市过去30天下单数最大的客户是那几个？
5. 门店ID=15062 的商户下单量最大的商品是哪些？列出他过去30天的下单商品详细信息
6. 安佳淡奶油在全国各个仓库的库存情况是怎样的？
7. 门店ID=15062 的最近30天内的拜访记录，深度分析它为什么流失了。
"""

    @staticmethod
    def parse_message_content(message_content: str) -> dict:
        """解析消息内容

        Args:
            message_content: 消息内容JSON字符串

        Returns:
            dict: 解析后的消息内容字典
        """
        try:
            return json.loads(message_content)
        except json.JSONDecodeError as e:
            logger.error(f"解析消息内容失败: {e}")
            return {}

    @staticmethod
    def check_is_group_chat(chat_type: str) -> bool:
        """检查是否为群聊

        Args:
            chat_type: 聊天类型

        Returns:
            bool: 如果是群聊则返回 True，否则返回 False
        """
        return chat_type == "group"

    @staticmethod
    def check_bot_mentioned(mentions: list, bot_name: str = None) -> bool:
        """检查机器人是否被@

        Args:
            mentions: 提及列表（可能是字典列表或MentionEvent对象列表）
            bot_name: 机器人名称（可选，默认从配置获取）

        Returns:
            bool: 如果机器人被@则返回 True，否则返回 False
        """
        if not mentions:
            return False

        # 如果没有提供机器人名称，从配置中获取
        if bot_name is None:
            bot_name = FeishuConfig.get_bot_name()

        for mention in mentions:
            # 处理字典格式的提及（测试或旧版本）
            if isinstance(mention, dict):
                if mention.get("name") == bot_name:
                    return True
            # 处理MentionEvent对象（实际运行时）
            elif hasattr(mention, 'name'):
                if mention.name == bot_name:
                    return True
            # 记录未知格式的提及对象
            else:
                logger.warning(f"未知的提及对象格式: {type(mention)}, {mention}")
        return False

    @staticmethod
    def check_message_starts_with_colon(user_query: str) -> bool:
        """检查消息是否以冒号开头（支持中文冒号和英文冒号）

        Args:
            user_query: 用户查询内容

        Returns:
            bool: 如果消息以冒号开头则返回 True，否则返回 False
        """
        stripped_query = user_query.strip()
        return stripped_query.startswith(":") or stripped_query.startswith("：")

    @staticmethod
    def should_skip_message(content_data: dict, user_query: str, chat_type: str = None, mentions: list = None) -> tuple[bool, str]:
        """判断是否应该跳过处理消息

        Args:
            content_data: 解析后的消息内容
            user_query: 用户查询内容
            chat_type: 聊天类型（可选）
            mentions: 提及列表（可选）

        Returns:
            tuple[bool, str]: (是否跳过, 跳过原因)
        """
        # 检查是否包含@_all
        if MessageParser.check_content_has_at_all(content_data):
            return True, "消息包含 @_all"

        # 检查消息文本是否为空
        if not user_query:
            return True, "消息文本为空"

        # 群聊场景下的特殊处理
        if MessageParser.check_is_group_chat(chat_type):
            # 检查是否@了机器人
            bot_mentioned = MessageParser.check_bot_mentioned(mentions)
            # 检查是否以冒号开头
            starts_with_colon = MessageParser.check_message_starts_with_colon(user_query)

            # 在群聊中，只处理@了机器人或以冒号开头的消息
            if not bot_mentioned and not starts_with_colon:
                return True, "群聊中未@机器人且不以冒号开头"

        return False, ""
