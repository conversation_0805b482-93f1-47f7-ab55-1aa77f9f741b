from datetime import datetime
import json
import aiohttp
import asyncio
from src.utils.logger import logger


async def delete_document(app_token: str, access_token: str, doc_type="bitable"):
    """
    Deletes a document (Bitable) in Feishu.

    Args:
        app_token (str): The app_token of the Bitable to delete.
        access_token (str): The access token for Feishu API.
        doc_type (str, optional): The type of document to delete. Defaults to "bitable".
            Other possible values might be "doc", "sheet", etc.  Check Feishu API documentation.

    Returns:
        dict: A dictionary containing the result of the deletion attempt.
              Returns None if an exception occurs.
    """
    try:
        url = f"https://open.feishu.cn/open-apis/drive/v1/files/{app_token}?type={doc_type}"
        headers = {
            "Authorization": f"Bearer {access_token}",
        }
        async with aiohttp.ClientSession() as session:
            async with session.delete(url, headers=headers) as response:
                response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
                return await response.json()
    except aiohttp.ClientError as e:
        logger.error(f"Error deleting document: {e}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        return None


from dataclasses import dataclass
from typing import Optional, Any

@dataclass
class BitableUploadResult:
    status: str
    ticket: Optional[str] = None
    url: Optional[str] = None
    error: Optional[str] = None
    detail: Optional[Any] = None
    job_status: Optional[int] = None

async def create_bitable_and_upload_csv(
    csv_content: str, access_token: str, name: str = None, folder_token: str = None
) -> BitableUploadResult:
    """
    Creates a Feishu Bitable and uploads CSV content to it.

    Args:
        csv_content (str): The content of the CSV file as a string.
        access_token (str): The access token for Feishu API.
        name (str, optional): The name of the Bitable. Defaults to "ChatBI数据导入YYYY-MM-DD-HH-MM-SS".
        folder_token (str, optional): The folder token where the Bitable should be created. If None, creates in root directory.

    Returns:
        BitableUploadResult: An object containing the result of the operation.
    """
    try:
        if name is None:
            time_str = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
            name = f"ChatBI数据导入{time_str}"
        app_token_response = await create_bitable_for_name(
            name=name, access_token=access_token, folder_token=folder_token
        )
        # 检查飞书API的返回结果, 如果创建失败, data中没有app
        if (
            not app_token_response
            or "app" not in app_token_response
            or "app_token" not in app_token_response["app"]
        ):
            logger.error(f"创建多维表失败: {app_token_response}")
            return BitableUploadResult(
                status="failed",
                error="Failed to create bitable",
                detail=app_token_response,
            )

        app_token = app_token_response["app"]["app_token"]
        logger.info(f"多维表创建成功, app_token: {app_token}")

        upload_result = await upload_media(
            csv_content=csv_content, access_token=access_token, parent_node=app_token
        )
        if (
            not upload_result
            or upload_result.get("code") != 0
            or "data" not in upload_result
            or "file_token" not in upload_result["data"]
        ):
            logger.error(f"上传CSV文件失败: {upload_result}")
            return BitableUploadResult(
                status="failed",
                error="Failed to upload CSV file",
                detail=upload_result,
            )

        file_token = upload_result["data"]["file_token"]
        logger.info(f"CSV文件上传成功, file_token: {file_token}")

        url = "https://open.feishu.cn/open-apis/drive/v1/import_tasks"
        payload = {
            "file_extension": "csv",
            "file_token": file_token,
            "type": "bitable",
            "file_name": name,
            "point": {"mount_type": 1, "mount_key": folder_token},
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}",
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                response_text = await response.text()
                logger.info(f"请求导入CSV到多维表结果:{response_text}")
                response_json = await response.json()
        if not response_json.get("data") or not response_json.get("data").get("ticket"):
            logger.error(f"请求导入CSV到多维表失败, 响应内容: {response_text}")
            return BitableUploadResult(
                status="failed",
                error="Failed to initiate CSV import",
                detail=response_json,
            )

        ticket = response_json["data"]["ticket"]
        logger.info(f"导入CSV到多维表任务提交成功, ticket: {ticket}")

        status_url = f"https://open.feishu.cn/open-apis/drive/v1/import_tasks/{ticket}"
        status_headers = {
            "Authorization": f"Bearer {access_token}",
        }
        async with aiohttp.ClientSession() as session:
            for i in range(40):
                async with session.get(status_url, headers=status_headers) as status_response:
                    status_response_text = await status_response.text()
                    status_data = await status_response.json()
                    logger.info(f"查询导入CSV到多维表的状态结果:{status_response_text}")
                    job_status = status_data.get("data", {}).get("result", {}).get("job_status")
                    if job_status == 2:
                        logger.info(f"导入CSV到多维表的状态为处理中, 第{i+1}次查询")
                    elif job_status == 0:
                        logger.info(f"导入CSV到多维表的状态为成功:{status_data}")
                        await delete_document(app_token=app_token, access_token=access_token)
                        return BitableUploadResult(
                            status="success",
                            ticket=ticket,
                            url=status_data.get("data", {}).get("result", {}).get("url"),
                        )
                    else:
                        logger.error(
                            f"导入CSV到多维表的状态为失败, 状态码: {job_status}, 错误信息: {status_data}"
                        )
                        return BitableUploadResult(
                            status="failed",
                            job_status=job_status,
                            detail=status_data,
                        )
                await asyncio.sleep(1)
        logger.warning("查询导入CSV到多维表状态超时")
        return BitableUploadResult(status="timeout", ticket=ticket)

    except aiohttp.ClientError as e:
        logger.error(f"请求Feishu API时发生网络错误: {e}")
        return BitableUploadResult(
            status="failed",
            error="Network error during Feishu API call",
            detail=str(e),
        )
    except Exception as e:
        logger.exception("创建多维表并上传CSV过程中发生未知错误")
        return BitableUploadResult(
            status="failed",
            error="Unknown error",
            detail=str(e),
        )

async def create_bitable_for_name(name: str, access_token: str, folder_token: str = None):
    # post this json to feishu API to create new doc:
    url = "https://open.feishu.cn/open-apis/bitable/v1/apps"
    payload = {"name": name}
    
    # 如果指定了文件夹token，则在指定文件夹下创建
    if folder_token:
        payload["folder_token"] = folder_token
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}",
    }
    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            response_text = await response.text()
            logger.info(f"create feishu file response:{response_text}")
            
            # 检查是否是认证错误
            if response.status == 400:
                try:
                    error_json = await response.json()
                    if error_json.get("code") == 99991661:  # Missing access token
                        logger.error(f"飞书API认证失败，access_token无效或过期: {error_json}")
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message="飞书访问令牌无效或已过期，请重新登录"
                        )
                except Exception as json_error:
                    logger.error(f"解析错误响应失败: {json_error}")
            
            response.raise_for_status()
            response_json = await response.json()
            return response_json["data"]


async def upload_media(csv_content: str, access_token: str, parent_node: str):
    time_str = datetime.now().strftime("%m%d%H%M%S")
    # Calculate the size of the CSV content in bytes
    file_size = len(csv_content.encode("utf-8"))
    url = "https://open.feishu.cn/open-apis/drive/v1/medias/upload_all"

    # 创建 FormData 对象
    data = aiohttp.FormData()
    data.add_field('file_name', f"ChatBI自动导入{time_str}.csv")
    data.add_field('parent_type', 'bitable_file')
    data.add_field('parent_node', parent_node)
    data.add_field('size', str(file_size))
    data.add_field('extra', json.dumps({"drive_route_token": parent_node}))
    data.add_field('file',
                   csv_content.encode('utf-8'),
                   filename=f"ChatBI自动导入{time_str}.csv",
                   content_type='text/csv')

    headers = {
        "Authorization": f"Bearer {access_token}",
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, data=data) as response:
            response_text = await response.text()
            logger.info(f"upload media response: {response_text}")
            response.raise_for_status()
            return await response.json()


if __name__ == "__main__":
    import asyncio
    asyncio.run(create_bitable_and_upload_csv(
        csv_content="测试,YorN\n好的,Y",
        access_token="u-dYwU6Hr3xbkG_sfzVClyVrk5iU7Nlhlboi0014EawwsD",
        name="一篇新的多维表格",
    ))
