"""
飞书Agent服务模块
负责Agent的初始化和管理
"""

from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.bots.coordinator_bot import CoordinatorBot
# 保留原有的MasterControllerBot导入以备兼容性需要
from src.services.agent.bots.master_controller_bot import MasterControllerBot
from src.utils.logger import logger
from src.utils.image_utils import download_image_to_base64
from .user_service import UserService


class AgentService:
    """Agent服务类"""

    @staticmethod
    async def initialize_agent_and_user_info(
        user_info_dict: dict
    ) -> tuple[BaseBot, dict]:
        """初始化Agent和用户信息对象

        Args:
            user_info_dict: 用户信息字典

        Returns:
            tuple: (agent, user_info_obj)
        """
        # 处理用户信息
        processed_user_info = UserService.process_user_info(user_info_dict)

        # 创建CoordinatorBot实例（新的Agent as Tool架构）
        bot: BaseBot = CoordinatorBot(processed_user_info)

        # 创建UserInfo对象
        user_info_obj = UserService.create_user_info_object(processed_user_info)

        logger.info(
            f"Agent和用户信息对象初始化完成，用户: {processed_user_info.get('name')}"
        )

        return bot, user_info_obj

    @staticmethod
    def extract_structured_assistant_message(result) -> dict | None:
        """提取结构化的助手消息

        Args:
            result: Agent运行结果

        Returns:
            dict | None: 结构化的助手消息，如果提取失败返回None
        """
        try:
            input_list = result.to_input_list()
            # 检查列表是否非空，最后一个元素是否为字典，且角色是否为 'assistant'
            if (
                input_list
                and isinstance(input_list[-1], dict)
                and input_list[-1].get("role") == "assistant"
            ):
                structured_message = input_list[-1]
                logger.info(
                    f"提取到的结构化助手消息: {str(structured_message)[:200]}..."
                )  # 记录前200字符
                return structured_message
            else:
                logger.warning(
                    f"无法从 to_input_list() 提取有效的助手消息: {input_list}"
                )
                return None
        except Exception as e:
            logger.error(f"提取 to_input_list() 时出错: {e}", exc_info=True)
            return None

    @staticmethod
    def prepare_input_messages(
        history: list, user_query: str, image_url: str | None = None
    ) -> list:
        """准备输入消息，包括历史记录

        Args:
            history: 历史消息列表
            user_query: 当前用户查询
            image_url: 图片URL, 如果存在则添加到输入消息中
        Returns:
            list: 格式化后的输入消息列表
        """
        input_messages = []
        if history and isinstance(history, list):
            # 添加历史消息，但排除最后一条用户消息（因为我们会单独添加当前查询）
            for msg in history:
                if msg["role"] == "user" and msg["content"] == user_query:
                    continue
                input_messages.append(msg)
            logger.info(f"添加了 {len(input_messages)} 条历史消息到AI输入")

        # 添加当前用户查询
        if image_url and len(image_url) > 60:
            # 如果图片不是base64编码，先下载并转换
            processed_image_url = image_url
            if not image_url.startswith('data:image/'):
                logger.info(f"下载并转换图片为base64编码: {image_url[:60]}...")
                processed_image_url = download_image_to_base64(image_url)

            if processed_image_url:
                input_messages.append(
                    {
                        "role": "user",
                        "content": [
                            {"type": "input_text", "text": user_query},
                            {
                                "type": "input_image",
                                "image_url": processed_image_url,
                                "detail": "auto",
                            },
                        ],
                    }
                )
                logger.info(f"添加了处理后的图片到AI输入")
            else:
                logger.warning("图片处理失败，回退到纯文本消息")
                input_messages.append({"role": "user", "content": user_query})
        else:
            input_messages.append({"role": "user", "content": user_query})
        return input_messages
