"""
飞书消息去重模块
负责管理消息去重逻辑，防止重复处理相同消息
"""
import time
from src.utils.logger import logger
from .config import FeishuConfig


class MessageDeduplicator:
    """消息去重器"""
    
    def __init__(self):
        # 用于跟踪已处理的消息ID
        self._processed_messages = {}
    
    def is_message_processed(self, message_id: str) -> bool:
        """检查消息是否已被处理
        
        Args:
            message_id: 消息ID
            
        Returns:
            bool: 如果消息已被处理返回True，否则返回False
        """
        if message_id in self._processed_messages:
            logger.info(
                f"消息:{message_id}已处理:{self._processed_messages[message_id]}"
            )
            return True
        return False
    
    def mark_message_processed(self, message_id: str):
        """标记消息为已处理
        
        Args:
            message_id: 消息ID
        """
        self._processed_messages[message_id] = time.time()
        logger.info(f"即将处理消息:{message_id}")
        
        # 定期清理旧条目以防止内存使用过多
        self._cleanup_old_entries()
    
    def _cleanup_old_entries(self):
        """清理旧的消息记录"""
        cleanup_threshold, cutoff_hours = FeishuConfig.get_cleanup_config()
        
        if len(self._processed_messages) > cleanup_threshold:
            cutoff_time = time.time() - (cutoff_hours * 3600)  # 转换为秒
            self._processed_messages = {
                k: v for k, v in self._processed_messages.items() 
                if v > cutoff_time
            }
            logger.info(f"清理了旧的消息记录，当前记录数: {len(self._processed_messages)}")
    
    def get_processed_count(self) -> int:
        """获取已处理消息数量
        
        Returns:
            int: 已处理消息数量
        """
        return len(self._processed_messages)


# 全局去重器实例
message_deduplicator = MessageDeduplicator()
