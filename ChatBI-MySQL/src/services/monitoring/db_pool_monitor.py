"""
数据库连接池监控模块

该模块提供数据库连接池的实时监控和统计信息。
"""

import time
import asyncio
from typing import Dict, Any
from src.utils.logger import logger
from src.db.connection import chatbi_db_pool, business_db_pool


class DatabasePoolMonitor:
    """数据库连接池监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.stats_history = []
        self.max_history_size = 100
        
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        stats = {
            "timestamp": time.time(),
            "chatbi_pool": self._get_single_pool_stats("ChatBI", chatbi_db_pool),
            "business_pool": self._get_single_pool_stats("Business", business_db_pool)
        }
        return stats
    
    def _get_single_pool_stats(self, pool_name: str, pool) -> Dict[str, Any]:
        """获取单个连接池的统计信息"""
        if pool is None:
            return {
                "pool_name": pool_name,
                "status": "not_initialized",
                "pool_size": 0,
                "active_connections": 0,
                "available_connections": 0
            }
        
        try:
            # 获取连接池的基本信息
            pool_size = pool.pool_size
            
            # 尝试获取活跃连接数（这个可能因MySQL Connector版本而异）
            active_connections = 0
            available_connections = pool_size
            
            # 如果连接池有相关属性，尝试获取更详细的信息
            if hasattr(pool, '_cnx_queue'):
                available_connections = pool._cnx_queue.qsize()
                active_connections = pool_size - available_connections
            
            return {
                "pool_name": pool_name,
                "status": "active",
                "pool_size": pool_size,
                "active_connections": active_connections,
                "available_connections": available_connections,
                "utilization_rate": active_connections / pool_size if pool_size > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取连接池 {pool_name} 统计信息时出错: {e}")
            return {
                "pool_name": pool_name,
                "status": "error",
                "error": str(e),
                "pool_size": 0,
                "active_connections": 0,
                "available_connections": 0
            }
    
    async def start_monitoring(self, interval_seconds: int = 30):
        """开始监控连接池"""
        if self.monitoring:
            logger.warning("连接池监控已在运行")
            return
        
        self.monitoring = True
        logger.info(f"开始监控数据库连接池，间隔: {interval_seconds}秒")
        
        try:
            while self.monitoring:
                stats = self.get_pool_stats()
                
                # 记录统计信息
                self._record_stats(stats)
                
                # 检查连接池健康状况
                self._check_pool_health(stats)
                
                await asyncio.sleep(interval_seconds)
                
        except Exception as e:
            logger.error(f"连接池监控出错: {e}", exc_info=True)
        finally:
            self.monitoring = False
            logger.info("连接池监控已停止")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
    
    def _record_stats(self, stats: Dict[str, Any]):
        """记录统计信息到历史记录"""
        self.stats_history.append(stats)
        
        # 限制历史记录大小
        if len(self.stats_history) > self.max_history_size:
            self.stats_history.pop(0)
    
    def _check_pool_health(self, stats: Dict[str, Any]):
        """检查连接池健康状况并发出警告"""
        for pool_type in ["chatbi_pool", "business_pool"]:
            pool_stats = stats.get(pool_type, {})
            
            if pool_stats.get("status") != "active":
                logger.error(f"连接池 {pool_stats.get('pool_name')} 状态异常: {pool_stats.get('status')}")
                continue
            
            utilization = pool_stats.get("utilization_rate", 0)
            pool_name = pool_stats.get("pool_name")
            
            # 连接池使用率警告
            if utilization > 0.9:
                logger.warning(f"连接池 {pool_name} 使用率过高: {utilization:.2%}")
            elif utilization > 0.8:
                logger.info(f"连接池 {pool_name} 使用率较高: {utilization:.2%}")
            
            # 可用连接数警告
            available = pool_stats.get("available_connections", 0)
            if available <= 1:
                logger.warning(f"连接池 {pool_name} 可用连接数过低: {available}")
    
    def get_recent_stats(self, count: int = 10) -> list:
        """获取最近的统计记录"""
        return self.stats_history[-count:] if self.stats_history else []
    
    def get_pool_summary(self) -> Dict[str, Any]:
        """获取连接池摘要信息"""
        current_stats = self.get_pool_stats()
        
        summary = {
            "monitoring_active": self.monitoring,
            "last_update": current_stats.get("timestamp"),
            "pools": {}
        }
        
        for pool_type in ["chatbi_pool", "business_pool"]:
            pool_stats = current_stats.get(pool_type, {})
            summary["pools"][pool_type] = {
                "name": pool_stats.get("pool_name"),
                "status": pool_stats.get("status"),
                "utilization": pool_stats.get("utilization_rate", 0),
                "active_connections": pool_stats.get("active_connections", 0),
                "pool_size": pool_stats.get("pool_size", 0)
            }
        
        return summary


# 全局连接池监控器实例
db_pool_monitor = DatabasePoolMonitor()


async def start_db_monitoring():
    """启动数据库连接池监控"""
    await db_pool_monitor.start_monitoring(interval_seconds=30)


def get_db_pool_status() -> Dict[str, Any]:
    """获取数据库连接池状态（同步接口）"""
    return db_pool_monitor.get_pool_summary()
