# 推荐问题API接口文档

## 概述

推荐问题API接口提供了获取AI推荐问题的功能。该接口基于用户的历史查询记录和其他用户的查询记录，使用AI生成个性化的问题推荐。

## 接口信息

- **URL**: `/api/recommendations`
- **方法**: `GET`
- **认证**: 需要用户登录（使用飞书登录）

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| count | int | 否 | 6 | 推荐问题数量，范围：1-10 |

## 响应格式

### 成功响应

```json
{
  "success": true,
  "recommendations": [
    "我的客户有哪些客户在过去60天买过protag纯牛奶整箱的，列出他们的店名和手机号码，加上购买protag纯牛奶整箱的件数。",
    "杭州市过去30天下单数最大的客户是那几个？",
    "门店ID=15062 的商户下单量最大的商品是哪些？列出他过去30天的下单商品详细信息",
    "安佳淡奶油在全国各个仓库的库存情况是怎样的？",
    "我的客户中，过去30天购买了安佳淡奶油的有哪些？列出他们的详细信息。",
    "门店ID=15062 的最近30天内的拜访记录，深度分析它为什么流失了。"
  ],
  "count": 6
}
```

### 无历史数据响应

```json
{
  "success": true,
  "recommendations": [],
  "count": 0,
  "message": "暂无历史数据，无法生成推荐问题"
}
```

### 错误响应

```json
{
  "success": false,
  "error": "错误描述信息"
}
```

## 状态码

- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 用户未登录或认证失败
- `500`: 服务器内部错误

## 使用示例

### JavaScript (前端)

```javascript
// 获取默认数量的推荐问题
fetch('/api/recommendations')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('推荐问题:', data.recommendations);
    } else {
      console.error('获取推荐失败:', data.error);
    }
  });

// 获取指定数量的推荐问题
fetch('/api/recommendations?count=3')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('推荐问题:', data.recommendations);
    } else {
      console.error('获取推荐失败:', data.error);
    }
  });
```

### Python (测试)

```python
import requests

# 获取推荐问题
response = requests.get('http://127.0.0.1:5700/api/recommendations?count=5')
data = response.json()

if data['success']:
    print(f"获取到 {data['count']} 个推荐问题:")
    for i, question in enumerate(data['recommendations'], 1):
        print(f"{i}. {question}")
else:
    print(f"获取推荐失败: {data['error']}")
```

### cURL

```bash
# 获取默认数量的推荐问题
curl -X GET "http://127.0.0.1:5700/api/recommendations" \
     -H "Cookie: access_token=your_access_token"

# 获取指定数量的推荐问题
curl -X GET "http://127.0.0.1:5700/api/recommendations?count=3" \
     -H "Cookie: access_token=your_access_token"
```

## 实现原理

1. **数据获取**: 从数据库获取当前用户最近10条查询记录和其他用户最近10条查询记录
2. **AI处理**: 使用UserQueryRecommendationBot调用AI模型分析历史数据
3. **问题生成**: AI根据历史数据生成个性化的推荐问题
4. **结果过滤**: 根据请求的数量参数截取最终结果

## 注意事项

1. **认证要求**: 必须先通过飞书登录获得有效的session
2. **数据依赖**: 推荐质量依赖于用户和其他用户的历史查询数据
3. **性能考虑**: AI处理可能需要几秒钟时间，建议设置合理的超时时间
4. **参数限制**: count参数会被自动限制在1-10范围内
5. **空数据处理**: 如果没有历史数据，会返回空的推荐列表

## 错误处理

常见错误及解决方案：

- **401 Unauthorized**: 用户未登录，需要先进行飞书登录
- **400 Bad Request**: 参数错误，检查count参数是否为有效整数
- **500 Internal Server Error**: 服务器内部错误，可能是AI服务异常或数据库连接问题

## 集成建议

1. **前端集成**: 可以在聊天界面添加"获取推荐问题"按钮
2. **缓存策略**: 可以考虑对推荐结果进行短时间缓存，避免频繁调用AI服务
3. **用户体验**: 在AI处理期间显示加载状态，提升用户体验
4. **错误提示**: 为不同的错误情况提供友好的用户提示
