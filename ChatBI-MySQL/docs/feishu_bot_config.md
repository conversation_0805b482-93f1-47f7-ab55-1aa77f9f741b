# 飞书机器人配置说明

## 机器人名称配置

### 概述
在群聊场景下，系统只会处理@了机器人或以冒号开头的消息。为了正确识别@机器人的消息，需要配置机器人的显示名称。

### 配置方法

#### 1. 环境变量配置（推荐）
在 `.env` 文件中设置：
```bash
FEISHU_BOT_NAME=你的机器人名称
```

#### 2. 默认配置
如果没有设置环境变量，系统将使用默认名称：`ChatBI测试`

### 配置示例

```bash
# 示例1：使用默认名称
# 不设置环境变量，系统自动使用 "ChatBI测试"

# 示例2：自定义机器人名称
FEISHU_BOT_NAME=智能助手

# 示例3：包含特殊字符的名称
FEISHU_BOT_NAME=ChatBI-生产环境
```

### 注意事项

1. **名称必须完全匹配**：配置的名称必须与飞书中机器人的实际显示名称完全一致（包括大小写、空格、特殊字符等）

2. **群聊消息处理规则**：
   - ✅ @了配置名称的机器人的消息会被处理
   - ✅ 以冒号开头的消息会被处理（支持英文冒号 `:` 和中文冒号 `：`）
   - ❌ 普通群聊消息会被忽略
   - ❌ @了其他用户的消息会被忽略

3. **私聊消息**：私聊消息不受此配置影响，所有私聊消息都会正常处理

### 验证配置

可以通过以下方式验证配置是否正确：

1. 在群聊中@机器人发送测试消息
2. 在群聊中发送以冒号开头的消息（如 `:测试` 或 `：测试`）
3. 查看日志确认消息是否被正确处理或跳过

### 故障排除

如果机器人在群聊中没有响应@消息：

1. 检查环境变量 `FEISHU_BOT_NAME` 是否设置正确
2. 确认配置的名称与飞书中显示的机器人名称完全一致
3. 重启应用以确保配置生效
4. 查看应用日志中的 "跳过处理" 信息

### 代码示例

```python
from src.services.feishu.config import FeishuConfig

# 获取当前配置的机器人名称
bot_name = FeishuConfig.get_bot_name()
print(f"当前机器人名称: {bot_name}")
```
