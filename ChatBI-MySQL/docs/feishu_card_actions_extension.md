# 飞书卡片操作扩展指南

## 概述

本文档介绍如何扩展飞书卡片的操作类型，实现新的业务功能。重构后的 `handle_card_action_trigger` 函数遵循 DRY 原则，具有良好的可扩展性。

## 架构设计

### 核心组件

1. **配置文件**: `src/services/feishu/action_handlers_config.py`
   - 定义所有操作类型的配置
   - 支持动态注册和注销操作

2. **事件处理器**: `src/services/feishu/event_handlers.py`
   - 统一的操作处理逻辑
   - 动态加载和执行配置的操作

### 配置结构

每个操作类型的配置包含以下字段：

```python
{
    "service_module": "模块路径",           # 业务服务模块
    "service_function": "函数名",          # 业务服务函数
    "service_params": {...},              # 额外的服务参数
    "after_action_module": "模块路径",     # 后续操作模块
    "after_action_function": "函数名",     # 后续操作函数
    "toast": {                           # 用户反馈消息
        "type": "success|info|error",
        "content": "反馈内容"
    }
}
```

## 如何添加新的操作类型

### 步骤1：创建业务服务

首先创建处理新操作的业务服务：

```python
# src/services/chatbot/favorite_service.py
def mark_favorite(conversation_id: str, is_favorite: bool, user_name: str):
    """标记对话为收藏"""
    # 实现收藏逻辑
    pass
```

### 步骤2：创建后续操作（可选）

如果需要后续操作（如发送通知），创建相应函数：

```python
# src/services/feishu/message_apis.py
def after_favorite_mark(card_id: str):
    """收藏标记后的操作"""
    # 实现后续操作逻辑
    pass
```

### 步骤3：添加配置

在 `action_handlers_config.py` 中添加新操作的配置：

```python
ACTION_HANDLERS_CONFIG["favorite"] = {
    "service_module": "src.services.chatbot.favorite_service",
    "service_function": "mark_favorite",
    "service_params": {"is_favorite": True},
    "after_action_module": "src.services.feishu.message_apis",
    "after_action_function": "after_favorite_mark",
    "toast": {
        "type": "success",
        "content": "已收藏，感谢反馈！",
    }
}
```

### 步骤4：更新前端卡片

在飞书卡片中添加新的按钮：

```json
{
    "tag": "button",
    "text": {
        "tag": "plain_text",
        "content": "收藏"
    },
    "type": "primary",
    "value": {
        "conversation_id": "对话ID",
        "card_id": "卡片ID",
        "action": "favorite"
    }
}
```

## 动态操作管理

### 运行时注册新操作

```python
from src.services.feishu.action_handlers_config import register_action_handler

# 动态注册新操作
register_action_handler("custom_action", {
    "service_module": "src.services.custom.custom_service",
    "service_function": "handle_custom_action",
    "service_params": {"custom_param": "value"},
    "after_action_module": "src.services.feishu.message_apis",
    "after_action_function": "after_custom_action",
    "toast": {
        "type": "info",
        "content": "自定义操作完成！",
    }
})
```

### 注销操作

```python
from src.services.feishu.action_handlers_config import unregister_action_handler

# 注销操作
success = unregister_action_handler("custom_action")
```

### 查询支持的操作

```python
from src.services.feishu.action_handlers_config import get_supported_actions

# 获取所有支持的操作
actions = get_supported_actions()
print(f"支持的操作: {actions}")
```

## 最佳实践

### 1. 命名规范
- 操作名使用下划线分隔：`good_case`, `bad_case`, `mark_favorite`
- 服务函数使用动词开头：`mark_good_case`, `share_conversation`

### 2. 错误处理
- 业务服务函数应该处理自己的异常
- 配置错误会被统一捕获并返回错误响应

### 3. 参数传递
- 所有业务服务函数都会接收 `conversation_id` 和 `user_name` 参数
- 额外参数通过 `service_params` 传递

### 4. 异步操作
- 后续操作会在1.5秒后异步执行
- 适合发送通知等不阻塞用户操作的任务

## 现有操作类型

### good_case
- **功能**: 标记对话为好案例
- **服务**: `mark_good_case`
- **后续操作**: `after_goodcase_mark`

### bad_case
- **功能**: 标记对话为坏案例
- **服务**: `mark_bad_case`
- **后续操作**: `after_badcase_mark`

## 故障排除

### 常见问题

1. **模块导入失败**
   - 检查模块路径是否正确
   - 确保模块文件存在

2. **函数不存在**
   - 检查函数名是否正确
   - 确保函数已定义并可访问

3. **参数错误**
   - 检查 `service_params` 配置
   - 确保业务函数接受正确的参数

### 调试技巧

1. 查看日志输出，了解具体错误信息
2. 使用 `get_supported_actions()` 确认操作已注册
3. 检查配置格式是否正确

## 总结

重构后的卡片操作处理系统具有以下优势：

1. **DRY原则**: 消除了重复代码，统一处理逻辑
2. **可扩展性**: 通过配置文件轻松添加新操作
3. **动态管理**: 支持运行时注册和注销操作
4. **错误处理**: 统一的错误处理和用户反馈
5. **维护性**: 配置与逻辑分离，便于维护

通过这种设计，添加新的卡片操作变得非常简单，只需要实现业务逻辑并添加配置即可。
