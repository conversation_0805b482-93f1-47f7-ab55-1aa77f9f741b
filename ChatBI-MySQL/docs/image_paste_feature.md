# 图片粘贴和多模态功能文档

## 功能概述

新增了图片粘贴和多模态AI对话功能，用户可以：
1. 在聊天输入框中粘贴图片
2. 自动上传图片到七牛云
3. 在输入框左下角显示图片缩略图
4. 发送包含图片的消息给AI进行多模态对话

## 前端功能

### 1. 图片粘贴上传

**位置**: `src/static/js/chatbi/services/uploadService.js`

**主要功能**:
- `uploadImage(file, onProgress)`: 上传图片到七牛云
- `extractImageFromPaste(event)`: 从粘贴事件中提取图片
- `validateImageFile(file)`: 验证图片文件
- `createImagePreviewUrl(file)`: 创建预览URL

**支持的图片格式**:
- JPEG (.jpeg, .jpg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

**文件大小限制**: 10MB

### 2. 聊天输入组件增强

**位置**: `src/static/js/chatbi/components/ChatInput.js`

**新增功能**:
- 粘贴事件监听器
- 图片上传状态管理
- 图片预览显示
- 上传进度显示
- 错误处理

**使用方法**:
1. 在输入框中按 `Ctrl+V` (Windows) 或 `Cmd+V` (Mac) 粘贴图片
2. 图片会自动上传并在左下角显示缩略图
3. 可以点击缩略图右上角的 ✕ 删除图片
4. 发送消息时会包含图片URL

### 3. 用户消息显示增强

**位置**: `src/static/js/chatbi/components/UserMessage.js`

**新增功能**:
- 显示用户上传的图片
- 图片网格布局（1张图片单列，多张图片双列）
- 点击图片放大查看
- 图片懒加载

### 4. 消息格式化器增强

**位置**: `src/static/js/chatbi/composables/useMessageFormatter.js`

**新增功能**:
- 解析包含图片的用户消息
- 支持JSON格式的消息内容
- 向后兼容纯文本消息

## 后端功能

### 1. 资源上传API

**位置**: `src/api/resource_api.py`

**端点**: `POST /api/upload`

**功能**:
- 接收multipart/form-data格式的文件上传
- 使用用户的summerfarm_api_token获取七牛云上传token
- 生成随机文件名避免冲突
- 返回文件URL

### 2. 查询API增强

**位置**: `src/api/query_api.py`

**新增功能**:
- 接收images参数（图片URL数组）
- 将图片信息传递给agent
- 保存包含图片的用户消息

### 3. 历史服务增强

**位置**: `src/services/chatbot/history_service.py`

**新增功能**:
- `save_user_message`函数支持images参数
- 将图片信息以JSON格式存储在content字段
- 向后兼容纯文本消息

### 4. Agent Runner增强

**位置**: `src/services/agent/runner.py`

**新增功能**:
- `run_agent_query`函数支持images参数
- 将图片URL添加到用户消息中
- 支持多模态AI对话

## 数据存储格式

### 用户消息存储

当用户发送包含图片的消息时，content字段存储JSON格式：

```json
{
  "text": "用户输入的文本内容",
  "images": [
    "https://azure.summerfarm.net/chatbi-resource/abc123...xyz.jpeg",
    "https://azure.summerfarm.net/chatbi-resource/def456...uvw.png"
  ]
}
```

纯文本消息仍然直接存储文本内容，保持向后兼容。

## 使用示例

### 前端使用

```javascript
// 监听粘贴事件
document.addEventListener('paste', async (event) => {
    const imageFile = extractImageFromPaste(event);
    if (imageFile) {
        try {
            const result = await uploadImage(imageFile);
            console.log('上传成功:', result.url);
        } catch (error) {
            console.error('上传失败:', error.message);
        }
    }
});
```

### 后端API调用

```javascript
// 发送包含图片的消息
const response = await fetch('/api/query', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        query: "请分析这张图片",
        images: [
            "https://azure.summerfarm.net/chatbi-resource/abc123...xyz.jpeg"
        ],
        conversation_id: "conv_123"
    })
});
```

## 安全考虑

1. **文件类型验证**: 只允许上传指定的图片格式
2. **文件大小限制**: 限制单个文件最大10MB
3. **用户认证**: 需要登录才能上传文件
4. **Token安全**: 使用用户专属的summerfarm_api_token
5. **随机文件名**: 防止文件名冲突和路径遍历攻击

## 错误处理

### 前端错误处理

- 文件类型不支持
- 文件大小超限
- 网络上传失败
- 用户未登录

### 后端错误处理

- Token无效或过期
- 七牛云API调用失败
- 数据库保存失败
- 参数验证失败

## 性能优化

1. **图片懒加载**: 用户消息中的图片使用懒加载
2. **预览URL管理**: 及时释放blob URL避免内存泄漏
3. **上传进度**: 显示上传进度提升用户体验
4. **错误重试**: 上传失败时可以重试

## 兼容性

- **向后兼容**: 现有的纯文本消息不受影响
- **浏览器支持**: 支持现代浏览器的Clipboard API
- **移动端**: 支持移动端的图片粘贴功能

## 测试建议

1. **功能测试**:
   - 粘贴不同格式的图片
   - 测试文件大小限制
   - 测试上传进度显示
   - 测试错误处理

2. **兼容性测试**:
   - 测试现有消息的显示
   - 测试不同浏览器的兼容性
   - 测试移动端功能

3. **性能测试**:
   - 测试大文件上传
   - 测试多图片同时上传
   - 测试内存使用情况

## 未来扩展

1. **拖拽上传**: 支持拖拽图片到输入框
2. **图片编辑**: 上传前的简单图片编辑功能
3. **更多文件类型**: 支持PDF、Word等文档类型
4. **批量上传**: 支持一次选择多个文件上传
5. **云端图片管理**: 用户上传历史和管理界面
