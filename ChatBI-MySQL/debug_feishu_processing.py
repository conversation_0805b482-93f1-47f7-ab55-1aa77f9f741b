#!/usr/bin/env python3
"""
调试飞书消息处理的脚本

用于测试和调试飞书消息处理流程中的问题。
"""

import sys
import os
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import logger
from src.services.feishu.query_processor import QueryProcessor

# 设置日志级别为DEBUG以查看详细信息
logging.getLogger('chat_bi_mysql').setLevel(logging.DEBUG)
# 也设置根日志级别
logging.basicConfig(level=logging.DEBUG)

async def test_feishu_message_processing():
    """测试飞书消息处理"""
    
    # 模拟用户信息
    user_info_dict = {
        "name": "测试用户",
        "email": "<EMAIL>",
        "open_id": "test_open_id",
        "union_id": "test_union_id"
    }
    
    # 模拟消息参数
    message_id = "test_message_123"
    user_query = "查询一下销售数据"
    root_id = "test_root_123"
    parent_id = None
    image_url = None
    
    logger.info("开始测试飞书消息处理")
    logger.info(f"用户查询: {user_query}")
    logger.info(f"用户信息: {user_info_dict}")
    
    try:
        # 调用查询处理器
        await QueryProcessor.handle_agent_query(
            message_id=message_id,
            user_query=user_query,
            user_info_dict=user_info_dict,
            root_id=root_id,
            parent_id=parent_id,
            image_url=image_url,
        )
        
        logger.info("飞书消息处理完成")
        
    except Exception as e:
        logger.error(f"飞书消息处理出错: {e}", exc_info=True)

async def main():
    """主函数"""
    logger.info("开始调试飞书消息处理")
    
    try:
        await test_feishu_message_processing()
    except Exception as e:
        logger.error(f"调试过程中出错: {e}", exc_info=True)
    
    logger.info("调试完成")

if __name__ == "__main__":
    asyncio.run(main())
